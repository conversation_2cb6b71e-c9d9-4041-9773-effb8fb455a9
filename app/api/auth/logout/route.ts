import { NextResponse } from "next/server";
import { clearUserSession } from "@/lib/auth";

export async function POST() {
  try {
    await clearUserSession();
    
    // Create a response that will be sent back to the client
    const response = NextResponse.json({ success: true });
    
    // Set cookies with expired dates to ensure they're removed from the client
    response.cookies.set("user_session", "", { 
      expires: new Date(0),
      path: "/"
    });
    response.cookies.set("auth", "", { 
      expires: new Date(0), 
      path: "/"
    });
    
    return response;
  } catch (error) {
    console.error("Logout error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
} 