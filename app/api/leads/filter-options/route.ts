import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { query } from "@/lib/db";
import { cacheManager } from "@/lib/cache";

// Cache filter options for 30 minutes since they don't change frequently
const FILTER_OPTIONS_CACHE_KEY = "leads:filter-options";
const FILTER_OPTIONS_TTL = 30 * 60 * 1000; // 30 minutes

interface FilterOptions {
  cantons: string[];
  businessStatuses: string[];
  addressGroupDescriptions: string[];
  sources: string[];
}

async function getFilterOptions(): Promise<FilterOptions> {
  // Check cache first
  const cached = cacheManager.get<FilterOptions>(FILTER_OPTIONS_CACHE_KEY);
  if (cached && !cacheManager.isStale(FILTER_OPTIONS_CACHE_KEY)) {
    return cached;
  }

  // Query distinct values for each filter field
  const [cantons, businessStatuses, addressGroupDescriptions, sources] = await Promise.all([
    query(`
      SELECT DISTINCT canton 
      FROM "bianchi_leads" 
      WHERE canton IS NOT NULL AND canton != '' AND country = 'Switzerland'
      ORDER BY canton
    `),
    query(`
      SELECT DISTINCT business_status 
      FROM "bianchi_leads" 
      WHERE business_status IS NOT NULL AND business_status != '' AND country = 'Switzerland'
      ORDER BY business_status
    `),
    query(`
      SELECT DISTINCT address_group_description 
      FROM "bianchi_leads" 
      WHERE address_group_description IS NOT NULL AND address_group_description != '' AND country = 'Switzerland'
      ORDER BY address_group_description
    `),
    query(`
      SELECT DISTINCT source 
      FROM "bianchi_leads" 
      WHERE source IS NOT NULL AND source != '' AND country = 'Switzerland'
      ORDER BY source
    `),
  ]);

  const filterOptions: FilterOptions = {
    cantons: cantons.map(row => row.canton),
    businessStatuses: businessStatuses.map(row => row.business_status),
    addressGroupDescriptions: addressGroupDescriptions.map(row => row.address_group_description),
    sources: sources.map(row => row.source),
  };

  // Cache the result
  cacheManager.set(FILTER_OPTIONS_CACHE_KEY, filterOptions, FILTER_OPTIONS_TTL);

  return filterOptions;
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    const filterOptions = await getFilterOptions();
    return NextResponse.json(filterOptions);
  } catch (error) {
    console.error("Error fetching filter options:", error);
    return NextResponse.json(
      { error: "Failed to fetch filter options" },
      { status: 500 }
    );
  }
}
