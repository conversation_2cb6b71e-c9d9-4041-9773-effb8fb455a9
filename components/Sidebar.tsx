"use client";

import { useState } from "react";
import Link from "next/link";
import {
  <PERSON>,
  <PERSON>,
  Settings,
  AlertCircle,
  ChevronLeft,
  ChevronRight,
  Bell,
  User,
} from "lucide-react";

const Sidebar = () => {
  const [isExpanded, setIsExpanded] = useState(true);

  return (
    <div
      className={`bg-primary text-white h-screen ${
        isExpanded ? "w-64" : "w-16"
      } flex flex-col transition-all duration-300 ease-in-out`}
    >
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="p-4 self-start text-white hover:text-gray-200"
      >
        {isExpanded ? <ChevronRight size={24} /> : <ChevronLeft size={24} />}
      </button>
      <nav className="flex-1">
        <Link
          href="/dashboard"
          className="flex items-center py-2 px-4 transition duration-200 hover:bg-red-700 hover:text-white"
        >
          <Home className="mr-2" size={20} />
          {isExpanded && <span>Dashboard</span>}
        </Link>
        <Link
          href="/leads"
          className="flex items-center py-2 px-4 transition duration-200 hover:bg-red-700 hover:text-white"
        >
          <List className="mr-2" size={20} />
          {isExpanded && <span>Leads</span>}
        </Link>
        <Link
          href="/logs"
          className="flex items-center py-2 px-4 transition duration-200 hover:bg-red-700 hover:text-white"
        >
          <AlertCircle className="mr-2" size={20} />
          {isExpanded && <span>Logs</span>}
        </Link>
      </nav>
      <div className="mt-auto pb-4">
        <Link
          href="/notifications"
          className="flex items-center py-2 px-4 transition duration-200 hover:bg-red-700 hover:text-white"
        >
          <Bell className="mr-2" size={20} />
          {isExpanded && <span>Notifications</span>}
        </Link>
        <Link
          href="/settings"
          className="flex items-center py-2 px-4 transition duration-200 hover:bg-red-700 hover:text-white"
        >
          <Settings className="mr-2" size={20} />
          {isExpanded && <span>Settings</span>}
        </Link>
        <Link
          href="/profile"
          className="flex items-center py-2 px-4 transition duration-200 hover:bg-red-700 hover:text-white"
        >
          <User className="mr-2" size={20} />
          {isExpanded && <span>Profile</span>}
        </Link>
      </div>
    </div>
  );
};

export default Sidebar;
