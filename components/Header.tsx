import Link from "next/link";
import Image from "next/image";

const Header = () => {
  return (
    <header className="sticky top-0 z-40 border-b bg-background">
      <div className="flex h-16 items-center px-6">
        <div className="flex items-center gap-2 lg:hidden">
          <Image
            src="/images/Bianchi_Logo.webp"
            alt="Bianchi Logo"
            width={40}
            height={40}
            priority
            className="object-contain"
          />
          <Link href="/" className="text-xl font-bold text-primary">
            Bianchi Leads Tool
          </Link>
        </div>
      </div>
    </header>
  );
};

export default Header;
