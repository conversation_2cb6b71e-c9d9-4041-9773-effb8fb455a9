import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import ee from '@google/earthengine'; // Use default import
// Import turf functions
import booleanPointInPolygon from '@turf/boolean-point-in-polygon';
import { Feature, Polygon, Point as GeoJsonPoint, Position } from 'geojson'; // Import GeoJSON types
// Import the entire H3 module
import * as h3 from 'h3-js';

// Debug mode for extra logging
const DEBUG_MODE = process.env.NODE_ENV !== 'production';
const DETAILED_DEBUG = process.env.DETAILED_DEBUG === 'true'; // More verbose logging

// --- Constants ---
const DEFAULT_MIN_RADIUS = 200; // Meters
const DEFAULT_MAX_RADIUS = 1000; // Meters
const START_H3_RESOLUTION = 5; // Start subdivision from this level
const MAX_H3_RESOLUTION = 11; // Do not subdivide beyond this level
const DENSITY_THRESHOLD = 700; // Persons per sq km. Subdivide if density exceeds this.
const GEE_BATCH_SIZE = 500; // Number of points to process per GEE call (increased slightly)
const GEE_POINT_SAMPLING_SCALE = 30; // Scale in meters for sampling point density

// --- Helper Types matching Frontend ---
type CirclePoint = {
    lat: number;
    lng: number;
    radius: number; // Final calculated radius in meters
    density?: number | null; // Population density (persons/km²) at this point
    h3Index?: string; // Optional: include the H3 index for debugging
};

// Represents an H3 cell being processed
type H3ProcessingState = {
    h3Index: string;
};

// Expected input from the frontend
type RequestBody = {
    polygon: Feature<Polygon>; // GeoJSON Polygon Feature defining the area of interest
    minRadius?: number; // Minimum search radius (meters) for dense areas
    maxRadius?: number; // Maximum search radius (meters) for sparse areas
};

// Define the mapping between population density and search radius
function getRadiusForDensity(density: number | null | undefined, minRadius: number, maxRadius: number): number {
    // Handle null, undefined, NaN, or non-positive density - default to maxRadius
    if (density === undefined || density === null || isNaN(density) || density <= 0) {
        if (DETAILED_DEBUG) console.log(`[DEBUG][getRadiusForDensity] Invalid density (${density}), returning maxRadius=${maxRadius}`);
        return maxRadius;
    }

    // Simplified linear interpolation between density thresholds
    // We can adjust these thresholds and the interpolation curve as needed.
    const lowDensityThreshold = 50; // Below this, use maxRadius
    const highDensityThreshold = 3000; // Above this, use minRadius

    let calculatedRadius: number;

    if (density <= lowDensityThreshold) {
        calculatedRadius = maxRadius;
    } else if (density >= highDensityThreshold) {
        calculatedRadius = minRadius;
    } else {
        // Linear interpolation between maxRadius and minRadius
        const densityRange = highDensityThreshold - lowDensityThreshold;
        const radiusRange = maxRadius - minRadius;
        const densityFraction = (density - lowDensityThreshold) / densityRange;
        // Inverse relationship: higher density -> smaller fraction -> closer to minRadius
        calculatedRadius = maxRadius - (densityFraction * radiusRange);
    }

    // Clamp the final radius to be within [minRadius, maxRadius] just in case
    const finalRadius = Math.max(minRadius, Math.min(maxRadius, calculatedRadius));
    if (DETAILED_DEBUG) console.log(`[DEBUG][getRadiusForDensity] Density=${density.toFixed(1)}, Calc Radius=${finalRadius.toFixed(1)} (Range [${minRadius}-${maxRadius}])`);
    return finalRadius;
}


// Earth Engine Authentication and Initialization
// Use import assertion for JSON files
import privateKey from "../../../../bianchi-leads-fb042fa763d1.json" assert { type: "json" };
let eeInitialized = false; // Flag to track initialization

async function authenticateEarthEngine() {
    if (eeInitialized) {
        if (DETAILED_DEBUG) console.log("[DEBUG][EE Auth] Earth Engine already initialized.");
        return;
    }
    if (DEBUG_MODE) console.log("[DEBUG][EE Auth] Entering authenticateEarthEngine");

    console.log("Attempting Earth Engine authentication...");
    try {
        await new Promise<void>((resolve, reject) => {
            ee.data.authenticateViaPrivateKey(privateKey, () => {
                console.log("Earth Engine authentication successful.");
                resolve();
            }, (error: string | Error) => {
                const errorMessage = typeof error === 'string' ? error : error.message;
                console.error("Earth Engine authentication failed:", errorMessage);
                reject(new Error(`Earth Engine authentication failed: ${errorMessage}`));
            });
        });

        console.log("Initializing Earth Engine...");
        await new Promise<void>((resolve, reject) => {
            ee.initialize(null, null, () => {
                console.log("Earth Engine initialization successful.");
                eeInitialized = true; // Set flag on success
                if (DEBUG_MODE) console.log("[DEBUG][EE Auth] Earth Engine initialization flag set to true.");
                resolve();
            }, (error: string | Error) => {
                const errorMessage = typeof error === 'string' ? error : error.message;
                console.error("Earth Engine initialization failed:", errorMessage);
                reject(new Error(`Earth Engine initialization failed: ${errorMessage}`));
            });
        });
        console.log("Earth Engine ready.");
        if (DEBUG_MODE) console.log("[DEBUG][EE Auth] Exiting authenticateEarthEngine successfully");
    } catch (error) {
        eeInitialized = false; // Ensure flag is false on error
        if (DEBUG_MODE) console.log("[DEBUG][EE Auth] Earth Engine initialization flag set to false due to error.");
        console.error("Error during Earth Engine setup:", error);
        throw error; // Re-throw
    }
}

// --- API Endpoint ---
export async function POST(req: NextRequest) {
  if (DEBUG_MODE) console.log(`[DEBUG][POST] /api/earth-engine/population-density (H3 Refactor) called at ${new Date().toISOString()}`);
  const startTime = Date.now();
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      if (DEBUG_MODE) console.warn("[DEBUG][Auth] Authentication failed: No session found.");
      return NextResponse.json({ error: "Authentication required" }, { status: 401 });
    }
    if (DEBUG_MODE) console.log("[DEBUG][Auth] Session authenticated for user ID:", session.user?.id);

    // Ensure Earth Engine is authenticated and initialized
    await authenticateEarthEngine();

    // Get polygon and parameters from request
    const {
        polygon: inputPolygon,
        minRadius = DEFAULT_MIN_RADIUS,
        maxRadius = DEFAULT_MAX_RADIUS,
    }: RequestBody = await req.json();

    if (DEBUG_MODE) console.log(`[DEBUG][Request] Parsed: minRadius=${minRadius}, maxRadius=${maxRadius}`);
    if (DETAILED_DEBUG) console.log("[DEBUG][Request] Received polygon object:", JSON.stringify(inputPolygon, null, 2));

    // Input validation
    if (!inputPolygon || inputPolygon.type !== 'Feature' || !inputPolygon.geometry || inputPolygon.geometry.type !== 'Polygon' || !Array.isArray(inputPolygon.geometry.coordinates)) {
      if (DEBUG_MODE) console.warn("[DEBUG][Validation] Invalid 'polygon' provided.");
      return NextResponse.json({ error: "Invalid 'polygon' provided. Expected GeoJSON Feature<Polygon>." }, { status: 400 });
    }
    // Simple check for valid coordinates within the polygon
    if (!inputPolygon.geometry.coordinates.every(ring => Array.isArray(ring) && ring.length >= 4 && ring.every(coord => Array.isArray(coord) && coord.length === 2 && typeof coord[0] === 'number' && typeof coord[1] === 'number'))) {
       if (DEBUG_MODE) console.warn("[DEBUG][Validation] Invalid coordinates within the polygon structure.");
       return NextResponse.json({ error: "Invalid coordinates within the polygon structure." }, { status: 400 });
    }

    if (typeof minRadius !== 'number' || typeof maxRadius !== 'number' || minRadius <= 0 || maxRadius < minRadius) {
       if (DEBUG_MODE) console.warn(`[DEBUG][Validation] Invalid radius parameters: minR=${minRadius}, maxR=${maxRadius}`);
       return NextResponse.json({ error: `Invalid radius parameters: minRadius=${minRadius}, maxRadius=${maxRadius}` }, { status: 400 });
    }

    console.log(`Starting H3 coverage generation for polygon. Start Res: ${START_H3_RESOLUTION}, Max Res: ${MAX_H3_RESOLUTION}, Density Threshold: ${DENSITY_THRESHOLD} p/km², Radius Range [${minRadius}m, ${maxRadius}m]`);

    // --- Core Logic ---
    const outputPoints: CirclePoint[] = await generateH3Coverage(
        inputPolygon,
        minRadius,
        maxRadius,
        DENSITY_THRESHOLD
    );

    // --- Post-processing and Response ---
    const endTime = Date.now();
    const durationSeconds = ((endTime - startTime) / 1000).toFixed(2);
    if (DEBUG_MODE) console.log(`[DEBUG][Result] Coverage generation complete. Found ${outputPoints.length} circles in ${durationSeconds}s.`);

    // Calculate statistics
    const validDensities = outputPoints
        .map(p => p.density)
        .filter((d): d is number => typeof d === 'number' && isFinite(d));

    const minDensity = validDensities.length > 0 ? Math.min(...validDensities) : null;
    const maxDensity = validDensities.length > 0 ? Math.max(...validDensities) : null;
    const avgDensity = validDensities.length > 0 ? validDensities.reduce((sum, d) => sum + d, 0) / validDensities.length : null;

    console.log("Density processing complete. Statistics:", {
      circleCount: outputPoints.length,
      minDensity: minDensity?.toFixed(1),
      maxDensity: maxDensity?.toFixed(1),
      avgDensity: avgDensity?.toFixed(1),
      duration: `${durationSeconds}s`,
    });
    if (DEBUG_MODE) console.log("[DEBUG] Sending successful response.");

    return NextResponse.json({
      points: outputPoints,
      count: outputPoints.length,
      statistics: {
        minDensity: minDensity,
        maxDensity: maxDensity,
        avgDensity: avgDensity,
        durationSeconds: parseFloat(durationSeconds),
      }
    });

  } catch (error) {
    const endTime = Date.now();
    const durationSeconds = ((endTime - startTime) / 1000).toFixed(2);
    console.error(`Error in population density coverage endpoint after ${durationSeconds}s:`, error);
    const message = error instanceof Error ? error.message : "Unknown error during H3 density coverage calculation";
     if (DEBUG_MODE && error instanceof Error) {
        console.error("Stack Trace:", error.stack);
     }
    if (DEBUG_MODE) console.log("[DEBUG] Sending error response.");
    return NextResponse.json({ error: message }, { status: 500 });
  }
}


// --- Core H3 Recursive Coverage Generation Function ---
async function generateH3Coverage(
    polygonFeature: Feature<Polygon>,
    minRadius: number,
    maxRadius: number,
    densityThreshold: number
): Promise<CirclePoint[]> {
    console.log(`[generateH3Coverage] Starting. Start Res: ${START_H3_RESOLUTION}, Max Res: ${MAX_H3_RESOLUTION}, Density Threshold: ${densityThreshold} p/km², Radius Range [${minRadius}m, ${maxRadius}m]`);
    if (DETAILED_DEBUG) console.log(`[DEBUG][H3 Coverage] Polygon Coords Sample:`, polygonFeature.geometry.coordinates[0].slice(0, 3));

    // Load GEE Image (using Population Density, persons per sq km)
    const densityImage = (ee.ImageCollection('CIESIN/GPWv411/GPW_Population_Density')
        .filter(ee.Filter.date('2020-01-01', '2020-12-31')) as ee.ImageCollection)
        .select('population_density')
        .first();

    if (!densityImage) {
        throw new Error("Could not load base Earth Engine population density image for 2020.");
    }
    if (DEBUG_MODE) console.log("[DEBUG][H3 Coverage] Earth Engine density image loaded.");

    // 1. Polyfill the input polygon with H3 cells at the starting resolution
    // h3.polygonToCells expects GeoJSON style polygon with [lat, lng] vertex order.
    // Input polygonFeature.geometry.coordinates is [lng, lat]. Need to swap.
    const polygonForH3 = polygonFeature.geometry.coordinates.map(ring =>
        ring.map(coord => [coord[1], coord[0]] as [number, number]) // Swap to [lat, lng]
    );

    // Use `polygonToCells` which returns cells whose centroids are within the polygon.
    // This seems more appropriate than `polyfill` which might include cells just intersecting.
    const initialH3Indexes = h3.polygonToCells(polygonForH3, START_H3_RESOLUTION, false); // `false` is for GeoJSON input

    if (initialH3Indexes.length === 0) {
        console.warn("[H3 Coverage] No H3 cell centroids found within the provided polygon at start resolution.", `Start Res: ${START_H3_RESOLUTION}`);
        return [];
    }
    if (DEBUG_MODE) console.log(`[DEBUG][H3 Coverage] Initial polyfill found ${initialH3Indexes.length} H3 cells at resolution ${START_H3_RESOLUTION}.`);

    // 2. Process H3 cells recursively
    const finalCircles: CirclePoint[] = [];
    const h3Queue: H3ProcessingState[] = initialH3Indexes.map(h3Index => ({ h3Index }));
    const processedH3 = new Set<string>(); // Track finalized/processed cells to avoid redundant GEE calls and processing

    let processedCount = 0;
    let geeCallCount = 0;

    while (h3Queue.length > 0) {
        const batchSize = Math.min(h3Queue.length, GEE_BATCH_SIZE);
        const currentBatchState = h3Queue.splice(0, batchSize);
        const currentH3Indexes = currentBatchState.map(s => s.h3Index).filter(idx => !processedH3.has(idx)); // Filter out already processed

        if (currentH3Indexes.length === 0) continue; // Skip if batch is empty after filtering

        if (DEBUG_MODE) console.log(`[DEBUG][H3 Proc] Processing batch of ${currentH3Indexes.length} (unique) H3 cells. Queue size: ${h3Queue.length}. First cell: ${currentH3Indexes[0]} (Res ${h3.getResolution(currentH3Indexes[0])})`);

        // Points for density check (GeoJSON Points [lng, lat])
        const pointsForDensityCheck: GeoJsonPoint[] = currentH3Indexes.map((h3Index: string) => {
            const [lat, lng] = h3.cellToLatLng(h3Index);
            return { type: "Point", coordinates: [lng, lat] };
        });

        // 3. Fetch densities for the batch
        geeCallCount++;
        if (DETAILED_DEBUG) console.log(`[DEBUG][H3 Proc] Making GEE call #${geeCallCount} for ${pointsForDensityCheck.length} points.`);
        const densityMap = await fetchDensitiesForPointsH3(pointsForDensityCheck, densityImage as ee.Image);
        if (DETAILED_DEBUG) console.log(`[DEBUG][H3 Proc] GEE call #${geeCallCount} complete. Got ${densityMap.size} densities.`);

        // 4. Decide: Finalize or Subdivide for each cell in the batch
        const nextLevelQueue: H3ProcessingState[] = [];
        for (const h3Index of currentH3Indexes) {
             // Double check if processed during GEE call or by a sibling's descendant marking
            if (processedH3.has(h3Index)) continue;

            const centerLatLng = h3.cellToLatLng(h3Index);
            const centerLngLat: Position = [centerLatLng[1], centerLatLng[0]]; // [lng, lat]
            const centerLngLatStr = `${centerLngLat[0]},${centerLngLat[1]}`;
            const density = densityMap.get(centerLngLatStr); // Density is persons/km²
            const currentResolution = h3.getResolution(h3Index);

            // --- Subdivision Decision ---
            const densityValue = (typeof density === 'number' && isFinite(density)) ? density : 0;
            const shouldSubdivide = densityValue > densityThreshold && currentResolution < MAX_H3_RESOLUTION;

            if (shouldSubdivide) {
                // Subdivide
                if (DETAILED_DEBUG) {
                    console.log(`[DEBUG][H3 Subdivide] Cell ${h3Index} (Res ${currentResolution}, Density ${density?.toFixed(1)}) -> Subdividing (Threshold ${densityThreshold}, Max Res ${MAX_H3_RESOLUTION})`);
                }
                const children = h3.cellToChildren(h3Index, currentResolution + 1);
                let childrenAdded = 0;
                children.forEach((childH3: string) => {
                    if (processedH3.has(childH3)) return; // Skip if already processed

                    const [childLat, childLng] = h3.cellToLatLng(childH3);
                    const childCenterLngLat: Position = [childLng, childLat];

                    // Check if the child's *center* is within the original polygon
                    const isChildCenterInside = booleanPointInPolygon(childCenterLngLat, polygonFeature);

                    if (isChildCenterInside) {
                         if (DETAILED_DEBUG) console.log(`[DEBUG][H3 Subdivide]   Child ${childH3} (Res ${currentResolution + 1}) center inside polygon. Adding to queue.`);
                        nextLevelQueue.push({ h3Index: childH3 });
                        childrenAdded++;
                    } else {
                        // Optimization: If a child's center is outside, it won't be processed, nor will its descendants. Mark it processed.
                        // processedH3.add(childH3); // Removed - let queue processing handle this filtering
                        if (DETAILED_DEBUG) console.log(`[DEBUG][H3 Subdivide]   Child ${childH3} (Res ${currentResolution + 1}) center OUTSIDE polygon. Skipping.`);
                    }
                });
                 if (DETAILED_DEBUG && childrenAdded === 0) console.log(`[DEBUG][H3 Subdivide]   No children of ${h3Index} added to queue (all centers outside polygon or already processed).`);

            } else {
                // Finalize this cell
                let reason = "";
                if (currentResolution >= MAX_H3_RESOLUTION) {
                    reason = `Max resolution (${MAX_H3_RESOLUTION}) reached.`;
                } else if (densityValue <= densityThreshold) {
                    reason = `Density (${density?.toFixed(1)}) <= threshold (${densityThreshold}).`;
                } else {
                     reason = `Unknown finalization reason (should not happen).`; // Should be covered by above
                }

                if (DETAILED_DEBUG) {
                    console.log(`[DEBUG][H3 Finalize] Cell ${h3Index} (Res ${currentResolution}, Density ${density?.toFixed(1)}). Reason: ${reason}`);
                }

                // Calculate final radius based on this cell's density
                const calculatedRadius = getRadiusForDensity(density, minRadius, maxRadius);
                const [lat, lng] = centerLatLng;

                // Final check: Ensure the *center* of the finalized cell is inside the polygon
                // (It should be, based on initial polyfill and child checks, but double-check)
                 const isCenterInside = booleanPointInPolygon(centerLngLat, polygonFeature);

                 if (isCenterInside) {
                    if (DETAILED_DEBUG) console.log(`[DEBUG][H3 Finalize]   Center [${lng.toFixed(4)}, ${lat.toFixed(4)}] INSIDE polygon. Adding circle (Radius: ${calculatedRadius.toFixed(1)}m).`);
                    finalCircles.push({
                        lat,
                        lng,
                        radius: calculatedRadius,
                        density: density,
                        h3Index: DEBUG_MODE ? h3Index : undefined // Include H3 index only in debug mode
                     });
                    processedCount++;
                 } else {
                     // This case is less likely if using polygonToCells and checking children, but log if it occurs.
                     if (DEBUG_MODE) console.warn(`[WARN][H3 Finalize]   Finalized cell ${h3Index} center [${lng.toFixed(4)}, ${lat.toFixed(4)}] was OUTSIDE polygon boundary. Skipping add.`);
                 }

                // Mark this cell as processed so children aren't re-processed if they appear in queue later
                processedH3.add(h3Index);
                // Optimization: Mark all potential descendants of this finalized cell as processed too, up to MAX_H3_RESOLUTION
                // This prevents adding children of finalized cells to the queue unnecessarily.
                // markDescendantsAsProcessed(h3Index, currentResolution, MAX_H3_RESOLUTION, processedH3); // Removed - This was causing issues, rely on queue filtering
            }
        }
        // Add the newly generated children (if any) to the main queue
        h3Queue.push(...nextLevelQueue);
    }

    console.log(`[generateH3Coverage] Finished. Generated ${finalCircles.length} circles. Total cells processed/finalized: ${processedCount}. GEE Calls: ${geeCallCount}.`);
    return finalCircles;
}


// --- Density Fetching (Adapted for H3 workflow) ---
async function fetchDensitiesForPointsH3(
    points: GeoJsonPoint[], // Array of GeoJSON Points [lng, lat]
    densityImage: ee.Image
): Promise<Map<string, number | null>> {
    const densityMap = new Map<string, number | null>();
    if (points.length === 0) {
        return densityMap;
    }

    if (DETAILED_DEBUG) console.log(`[DEBUG][fetchDensities] Fetching density for ${points.length} points using GEE.`);

    // Convert GeoJSON points to Earth Engine geometry points
    const eePoints = points.map(p => ee.Geometry.Point(p.coordinates as [number, number]));
    const featureCollection = new ee.FeatureCollection(eePoints.map((p: any) => new (ee as any).Feature(p)));

    // Sample density image at the points
    // Using 'firstNonNull' reducer to get the density value at the point location.
    // Increased scale slightly for potentially better results near pixel edges?
    const sampledFeatures = (densityImage as any).reduceRegions({
        collection: featureCollection,
        reducer: (ee as any).Reducer.firstNonNull(), // Get value at point
        scale: GEE_POINT_SAMPLING_SCALE, // Use defined scale
        // tileScale: 4 // Might help with memory limits on large requests
    });

    // Evaluate GEE result
    const result = await new Promise<any>((resolve, reject) => {
        sampledFeatures.evaluate((data: any, error: any) => {
            if (error) {
                console.error("[ERROR][fetchDensities] Earth Engine evaluation error:", error);
                reject(new Error(`Earth Engine evaluation error: ${error.message || error}`));
            } else {
                 if (DETAILED_DEBUG) console.log(`[DEBUG][fetchDensities] GEE evaluation successful. Features returned: ${data?.features?.length}`);
                resolve(data);
            }
        });
    });

    const features = (result as any)?.features || [];

    if(features.length !== points.length && DEBUG_MODE) {
        console.warn(`[WARN][fetchDensities] Mismatch between input points (${points.length}) and GEE results (${features.length}). Some points might be outside image bounds or in nodata areas.`);
    }

    // Map results back to the original points using coordinates as keys
    features.forEach((feature: any) => {
        if (!feature?.geometry?.coordinates || !feature?.properties) return;

        const coords = feature.geometry.coordinates as [number, number] | undefined;
        // The reducer was 'firstNonNull', so the property name is 'first'.
        // If GPW band name was 'population_density', and reducer was mean(), property would be 'population_density'.
        const densityValue = feature.properties.first;

        if (coords && typeof coords[0] === 'number' && typeof coords[1] === 'number') {
            const lng = coords[0];
            const lat = coords[1];
            // Ensure density is a valid number, otherwise null
            const density = (typeof densityValue === 'number' && isFinite(densityValue)) ? densityValue : null;
            const key = `${lng},${lat}`; // Use Lng,Lat consistent with input points array
            densityMap.set(key, density);
             if (DETAILED_DEBUG && density === null) console.log(`[DEBUG][fetchDensities]   Point (${lat.toFixed(4)}, ${lng.toFixed(4)}) received null density from GEE.`);
        } else if (DETAILED_DEBUG) {
            console.warn(`[DEBUG][fetchDensities] Skipping feature with invalid coordinates/properties:`, JSON.stringify(feature));
        }
    });

     // Ensure all input points have an entry in the map (even if null, indicates GEE didn't return a value)
    points.forEach(p => {
        const key = `${p.coordinates[0]},${p.coordinates[1]}`;
        if (!densityMap.has(key)) {
             if (DETAILED_DEBUG) console.log(`[DEBUG][fetchDensities] Point (${p.coordinates[1].toFixed(4)}, ${p.coordinates[0].toFixed(4)}) missing from GEE results, setting density to null.`);
            densityMap.set(key, null);
        }
    });

    if (DETAILED_DEBUG) console.log(`[DEBUG][fetchDensities] Mapped densities for ${densityMap.size} points (should match input count: ${points.length}).`);
    return densityMap;
}

// Ensure the file ends with a newline for some linters/formatters 