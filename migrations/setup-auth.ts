import { runMigration } from './create_auth_tables';
import { hash } from 'bcryptjs';
import { Pool } from 'pg';
import { v4 as uuidv4 } from 'uuid';
import dotenv from 'dotenv';
import { cleanupTables } from './clean-up-tables';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Create a database connection
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: {
    rejectUnauthorized: false, // Required for Neon
  },
});

async function setupAuth() {
  try {
    console.log('🚀 Starting Auth.js setup...');
    
    // Cleanup existing tables if needed
    if (process.env.CLEAN_BEFORE_SETUP === 'true') {
      console.log('Running cleanup before setup...');
      await cleanupTables();
    }
    
    // Run the migration to create Auth.js tables
    await runMigration();
    
    // Check if users table exists
    const tableExists = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'users'
      );
    `);
    
    if (!tableExists.rows[0].exists) {
      console.error('❌ Users table does not exist. Migration may have failed.');
      return;
    }
    
    // Create an admin user if one doesn't exist
    const adminUsername = process.env.ADMIN_USERNAME || 'admin';
    const adminPassword = process.env.ADMIN_PASSWORD || 'Admin123!';
    
    // Check if users table has the password column
    try {
      const tableInfo = await pool.query(`
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'password'
      `);
      
      if (tableInfo.rows.length === 0) {
        // Add password column if it doesn't exist
        console.log('Adding password column to users table...');
        await pool.query(`
          ALTER TABLE users 
          ADD COLUMN IF NOT EXISTS password TEXT
        `);
        console.log('✅ Added password column to users table');
      }
    } catch (error) {
      console.error('Error checking/updating users table schema:', error);
      throw error;
    }
    
    // Check if admin user already exists
    const existingAdminResult = await pool.query(
      'SELECT id FROM users WHERE username = $1',
      [adminUsername]
    );
    
    if (existingAdminResult.rows.length === 0) {
      // Generate a unique ID
      const userId = uuidv4();
      
      // Hash the password
      const hashedPassword = await hash(adminPassword, 10);
      
      // Create admin user with explicit ID
      await pool.query(
        'INSERT INTO users (id, name, username, password, role, created_at, updated_at) VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)',
        [userId, 'Admin', adminUsername, hashedPassword, 'admin']
      );
      
      console.log(`✅ Admin user created with username: ${adminUsername} and ID: ${userId}`);
      console.log('⚠️ Please change the admin password after first login!');
    } else {
      console.log('✅ Admin user already exists');
      
      // Update admin password if requested via environment variable
      if (process.env.RESET_ADMIN_PASSWORD === 'true') {
        const hashedPassword = await hash(adminPassword, 10);
        
        await pool.query(
          'UPDATE users SET password = $1 WHERE username = $2',
          [hashedPassword, adminUsername]
        );
        
        console.log('✅ Admin password has been reset');
      }
    }
    
    console.log('✅ Auth.js setup completed successfully!');
  } catch (error) {
    console.error('❌ Auth.js setup failed:', error);
    process.exit(1);
  } finally {
    // Close the database connection
    await pool.end();
  }
}

// Run the setup
setupAuth()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error('Setup script error:', error);
    process.exit(1);
  }); 