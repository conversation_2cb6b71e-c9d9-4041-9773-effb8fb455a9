# Database Migrations

This folder contains SQL migrations for the Bianchi Lead App database.

## Migration Files

- `create_leads_table.sql` - Creates the leads table with constraints and indexes
- `create_logs_table.sql` - Creates the logging table
- `create_users_table.sql` - Creates the users table with role-based access control

## Running Migrations

There are two ways to run migrations:

### Option 1: Using the NPM script

```bash
npm run migrate
```

This will run all migrations in the correct order.

### Option 2: Running individual migrations

For TypeScript:
```bash
npx tsx migrations/create_leads_table.ts
npx tsx migrations/create_users_table.ts
```

For JavaScript:
```bash
node migrations/create_leads_table.js
node migrations/create_users_table.js
```

## Migration Order

Migrations should be run in the following order:

1. `create_leads_table.sql`
2. `create_logs_table.sql`
3. `create_users_table.sql`

## Adding New Migrations

When adding a new migration:

1. Create a new SQL file with the table definition
2. Add a corresponding .ts or .js file to execute it
3. Add the SQL file to the `migrationFiles` array in `run-migrations.ts` 