"use client";

import React, { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  TableWrapper,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
} from "lucide-react";

interface SearchResultPoint {
  lat: number;
  lng: number;
  place_id?: string;
  name?: string;
}

interface PaginatedResultsTableProps {
  results: SearchResultPoint[];
  itemsPerPage?: number;
  onResultClick?: (result: SearchResultPoint) => void;
}

const PaginatedResultsTable: React.FC<PaginatedResultsTableProps> = ({
  results,
  itemsPerPage = 25,
  onResultClick,
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredResults, setFilteredResults] = useState<SearchResultPoint[]>(
    []
  );

  // Calculate total pages
  const totalPages = Math.ceil(filteredResults.length / itemsPerPage);

  // Get current page items
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredResults.slice(indexOfFirstItem, indexOfLastItem);

  // Filter results when search term changes
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredResults(results);
    } else {
      const term = searchTerm.toLowerCase();
      const filtered = results.filter(
        (result) =>
          result.name?.toLowerCase().includes(term) ||
          result.place_id?.toLowerCase().includes(term) ||
          `${result.lat}, ${result.lng}`.includes(term)
      );
      setFilteredResults(filtered);
    }
    // Reset to first page when filter changes
    setCurrentPage(1);
  }, [searchTerm, results]);

  // Handle page changes
  const goToPage = (page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  };

  const goToFirstPage = () => goToPage(1);
  const goToPreviousPage = () => goToPage(currentPage - 1);
  const goToNextPage = () => goToPage(currentPage + 1);
  const goToLastPage = () => goToPage(totalPages);

  // Handle result click
  const handleResultClick = (result: SearchResultPoint) => {
    if (onResultClick) {
      onResultClick(result);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-2">
        <Label htmlFor="search-results" className="w-24">
          Search:
        </Label>
        <Input
          id="search-results"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          placeholder="Filter by name or coordinates..."
          className="flex-1"
        />
      </div>

      <TableWrapper>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">#</TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Coordinates</TableHead>
              <TableHead className="hidden md:table-cell">Place ID</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {currentItems.length > 0 ? (
              currentItems.map((result, index) => (
                <TableRow
                  key={`${result.place_id || index}-${result.lat}-${
                    result.lng
                  }`}
                  className="cursor-pointer hover:bg-slate-100"
                  onClick={() => handleResultClick(result)}
                >
                  <TableCell className="font-medium">
                    {indexOfFirstItem + index + 1}
                  </TableCell>
                  <TableCell>{result.name || "Unnamed Location"}</TableCell>
                  <TableCell>
                    {result.lat.toFixed(6)}, {result.lng.toFixed(6)}
                  </TableCell>
                  <TableCell className="hidden md:table-cell text-xs truncate max-w-[200px]">
                    {result.place_id || "N/A"}
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={4} className="text-center py-4">
                  No results found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableWrapper>

      {filteredResults.length > 0 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-slate-500">
            Showing {indexOfFirstItem + 1}-
            {Math.min(indexOfLastItem, filteredResults.length)} of{" "}
            {filteredResults.length} results
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="icon"
              onClick={goToFirstPage}
              disabled={currentPage === 1}
            >
              <ChevronsLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={goToPreviousPage}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <span className="text-sm">
              Page {currentPage} of {totalPages}
            </span>
            <Button
              variant="outline"
              size="icon"
              onClick={goToNextPage}
              disabled={currentPage === totalPages}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={goToLastPage}
              disabled={currentPage === totalPages}
            >
              <ChevronsRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default PaginatedResultsTable;
