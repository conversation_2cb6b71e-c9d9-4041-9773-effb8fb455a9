"use client";

import { useEffect, useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { Clock, Building2 } from "lucide-react";
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";
import Link from "next/link";

interface DashboardStats {
  totalLeads: number;
  newLeadsThisMonth: number;
  newLeadsThisWeek: number;
  leadsBySource: { source: string; count: number }[];
  leadsByCity: { city: string; count: number }[];
  leadsOverTime: { month: string; count: number }[];
  monthlyGrowth: number;
  cantonDistribution: { canton: string; count: number; percentage: number }[];
  businessTypes: { type: string; count: number }[];
  leadsPerMonth: { month: string; count: number }[];
}

export default function DashboardPage() {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState<"last7" | "last30" | "last90" | "all">("all");

  useEffect(() => {
    async function fetchDashboardStats() {
      try {
        setLoading(true);
        console.log("Fetching dashboard stats...");
        const response = await fetch("/api/dashboard/stats");
        console.log("Response status:", response.status);
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        console.log("Received dashboard data:", data);
        
        if (data.error) {
          throw new Error(data.error);
        }
        
        setStats(data);
        setError(null);
        console.log("Dashboard data set successfully");
      } catch (error) {
        console.error("Failed to fetch dashboard stats:", error);
        setError(error instanceof Error ? error.message : "Failed to fetch data");
      } finally {
        setLoading(false);
      }
    }

    fetchDashboardStats();
  }, []);

  if (loading) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-lg">Loading dashboard data...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-lg text-red-500">Error: {error}</div>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-lg">No data available</div>
      </div>
    );
  }

  return (
    <div className="mx-auto p-2 sm:p-4">

      {/* Dashboard Summary */}
      <Card className="mb-6 bg-gradient-to-r from-primary/5 to-primary/10 border-none">
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-4 items-start">
            <div className="flex-1">
              <h3 className="text-lg font-medium mb-2">Dashboard Overview</h3>
              <p className="text-muted-foreground mb-4">
                Welcome to your restaurant lead management dashboard. Here&apos;s a summary of your current lead status:
              </p>
              <ul className="space-y-2">
                <li className="flex items-center gap-2">
                  <div className="h-2 w-2 rounded-full bg-primary"></div>
                  <span>You have <strong>{stats?.totalLeads || 0}</strong> total leads in the system</span>
                </li>
                <li className="flex items-center gap-2">
                  <div className="h-2 w-2 rounded-full bg-[hsl(var(--chart-success))]"></div>
                  <span><strong>{stats?.newLeadsThisMonth || 0}</strong> new leads this month</span>
                </li>
                <li className="flex items-center gap-2">
                  <div className="h-2 w-2 rounded-full bg-yellow-500"></div>
                  <span>Monthly growth rate: <strong>{stats?.monthlyGrowth || 0}%</strong></span>
                </li>
              </ul>
            </div>
            <div className="w-full md:max-w-[250px] flex flex-col gap-2">
              <Link href="/leads" className="w-full">
                <button className="w-full px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors">
                  View All Leads
                </button>
              </Link>
              <Link href="/logs" className="w-full">
                <button className="w-full px-4 py-2 bg-white border border-muted text-muted-foreground rounded-md hover:bg-muted/20 transition-colors">
                  View Activity Logs
                </button>
              </Link>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Adjusted grid for three summary cards */}
      <div className="grid grid-cols-1 gap-4 sm:gap-5 sm:grid-cols-2 lg:grid-cols-3">
        <Card className="bg-[hsl(var(--stat-background-1))] overflow-hidden">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Leads</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[hsl(var(--primary))]\">
              {stats?.totalLeads}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-[hsl(var(--stat-background-2))] overflow-hidden">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              New Leads (This Month)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[hsl(var(--chart-success))]">
              {stats?.newLeadsThisMonth}
            </div>
            <p className="text-xs text-muted-foreground pt-1">Current month</p>
          </CardContent>
        </Card>

        <Card className="bg-[hsl(var(--stat-background-3))] overflow-hidden">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              New Leads (This Week)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[hsl(var(--chart-info))]">
              {stats?.newLeadsThisWeek}
            </div>
            <p className="text-xs text-muted-foreground pt-1">Current week</p>
          </CardContent>
        </Card>
      </div>

      <div className="mt-4 sm:mt-8 grid grid-cols-1 gap-4 sm:gap-5">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-4 w-4" />
              Canton Distribution
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {stats.cantonDistribution.slice(0, 5).map((canton) => (
                <div key={canton.canton} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium">{canton.canton}</p>
                    <p className="text-sm text-muted-foreground">{canton.percentage}% ({canton.count} leads)</p>
                  </div>
                  <Progress value={canton.percentage} className="h-2" />
                </div>
              ))}
              {stats.cantonDistribution.length > 5 && (
                <button className="text-sm text-primary hover:underline mt-2">
                  View all {stats.cantonDistribution.length} cantons
                </button>
              )}
            </div>

            <div className="mt-6 flex justify-between items-center">
              <div>
                <p className="text-sm font-medium">Top Canton</p>
                <p className="text-lg font-bold">{stats.cantonDistribution[0]?.canton || "N/A"}</p>
              </div>
              <div>
                <p className="text-sm font-medium">Top Canton Share</p>
                <p className="text-lg font-bold text-primary">{stats.cantonDistribution[0]?.percentage || 0}%</p>
              </div>
              <div>
                <p className="text-sm font-medium">Covered Cantons</p>
                <p className="text-lg font-bold">{stats.cantonDistribution.length}/26</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex items-center justify-between">
            <CardTitle>Popular Business Types</CardTitle>
          </CardHeader>
          <CardContent>
            <ChartContainer
              config={{
                types: {
                  label: "Business Types",
                  color: "hsl(var(--chart-1))",
                },
              }}
              className="h-[250px] sm:h-[300px] w-full"
            >
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={stats.businessTypes}
                  layout="vertical"
                  margin={{ top: 5, right: 10, left: 10, bottom: 5 }}
                >
                  <XAxis 
                    type="number"
                    domain={[0, (dataMax: number) => Math.ceil(dataMax * 1.1)]}
                    tickCount={5}
                    tickFormatter={(value) => Math.round(value).toString()}
                  />
                  <YAxis 
                    type="category" 
                    dataKey="type"
                    width={80}
                    tick={{ 
                      fontSize: 10,
                      width: 75,
                    }}
                  />
                  <CartesianGrid strokeDasharray="3 3" opacity={0.7} />
                  <Tooltip
                    content={({ active, payload }) => {
                      if (active && payload && payload.length) {
                        return (
                          <div className="rounded-lg border bg-background p-3 shadow-md">
                            <div className="flex items-center gap-2 mb-1">
                              <div className="h-3 w-3 rounded-full" style={{ background: `hsl(var(--chart-${(payload[0].payload.index % 3) + 1}))` }}></div>
                              <p className="text-sm font-medium">
                                {payload[0].payload.type}
                              </p>
                            </div>
                            <p className="text-sm text-muted-foreground">
                              {payload[0].value} leads
                            </p>
                            <p className="text-xs text-muted-foreground mt-1">
                              {Math.round((payload[0].value as number) / (stats.totalLeads || 1) * 100)}% of total
                            </p>
                          </div>
                        );
                      }
                      return null;
                    }}
                  />
                  <Bar 
                    dataKey="count" 
                    fill="hsl(var(--chart-1))"
                    radius={[0, 4, 4, 0]}
                    animationDuration={1000}
                  >
                    {stats.businessTypes.map((entry, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={`hsl(var(--chart-${(index % 3) + 1}))`}
                      />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            </ChartContainer>
            
            <div className="mt-4 grid grid-cols-3 gap-2">
              {stats.businessTypes.slice(0, 3).map((type, index) => (
                <div key={index} className="bg-muted/30 p-2 rounded-md text-center">
                  <p className="text-xs text-muted-foreground">{type.type}</p>
                  <p className="text-sm font-medium">{type.count}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Lead Generation per Month
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between mb-4">
              <div>
                <p className="text-sm text-muted-foreground">Total for period</p>
                <p className="text-lg font-bold">{stats.leadsPerMonth.reduce((sum, item) => sum + item.count, 0)} leads</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Avg. per month</p>
                <p className="text-lg font-bold">{Math.round(stats.leadsPerMonth.reduce((sum, item) => sum + item.count, 0) / stats.leadsPerMonth.length)} leads</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Trend</p>
                <p className="text-lg font-bold text-green-600">+{stats.monthlyGrowth}%</p>
              </div>
            </div>
          
            <ChartContainer
              config={{
                leads: {
                  label: "Leads",
                  color: "hsl(var(--chart-4))",
                },
              }}
              className="h-[250px] sm:h-[300px] w-full"
            >
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={stats.leadsPerMonth}
                  margin={{ top: 5, right: 10, left: 5, bottom: 45 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="month"
                    height={60}
                    tick={props => (
                      <text
                        x={props.x}
                        y={props.y + 12}
                        textAnchor="end"
                        transform={`rotate(-45, ${props.x}, ${props.y})`}
                        className="text-[10px]"
                      >
                        {props.payload.value}
                      </text>
                    )}
                  />
                  <YAxis />
                  <Tooltip
                    content={({ active, payload }) => {
                      if (active && payload && payload.length) {
                        return (
                          <div className="rounded-lg border bg-background p-2 shadow-sm">
                            <p className="text-sm font-medium">
                              {payload[0].payload.month}
                            </p>
                            <p className="text-sm text-muted-foreground">
                              {payload[0].value} leads
                            </p>
                          </div>
                        );
                      }
                      return null;
                    }}
                  />
                  <Line
                    type="monotone"
                    dataKey="count"
                    stroke="hsl(var(--chart-4))"
                    strokeWidth={2}
                    dot={{
                      fill: "hsl(var(--chart-4))",
                      stroke: "hsl(var(--background))",
                      strokeWidth: 2,
                    }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </ChartContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Lead Sources Distribution</CardTitle>
            <div className="text-sm text-muted-foreground">
              Overview of where your leads are coming from
            </div>
          </CardHeader>
          <CardContent className="flex flex-col gap-4">
            <ChartContainer
              config={{
                source: {
                  label: "Sources",
                  color: "hsl(var(--chart-1))",
                },
              }}
              className="h-[250px] sm:h-[300px] w-full"
            >
              <ResponsiveContainer width="100%" height="100%">
                <PieChart margin={{ top: 0, right: 0, left: 0, bottom: 0 }}>
                  <Pie
                    data={stats.leadsBySource}
                    dataKey="count"
                    nameKey="source"
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    innerRadius={40}
                    paddingAngle={2}
                    label={({
                      cx,
                      cy,
                      midAngle,
                      innerRadius,
                      outerRadius,
                      value,
                      index,
                    }) => {
                      const RADIAN = Math.PI / 180;
                      const radius = outerRadius * 1.2;
                      const x = cx + radius * Math.cos(-midAngle * RADIAN);
                      const y = cy + radius * Math.sin(-midAngle * RADIAN);
                      return (
                        <text
                          x={x}
                          y={y}
                          fill="currentColor"
                          textAnchor={x > cx ? "start" : "end"}
                          dominantBaseline="central"
                          className="text-[10px] sm:text-xs"
                        >
                          {stats.leadsBySource[index].source} ({value})
                        </text>
                      );
                    }}
                  >
                    {stats.leadsBySource.map((entry, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={`hsl(var(--chart-${(index % 3) + 1}))`}
                        strokeWidth={1}
                      />
                    ))}
                  </Pie>
                  <Tooltip
                    content={({ active, payload }) => {
                      if (active && payload && payload.length) {
                        return (
                          <div className="rounded-lg border bg-background p-3 shadow-md">
                            <div className="flex items-center gap-2 mb-1">
                              <div 
                                className="h-3 w-3 rounded-full" 
                                style={{ 
                                  background: `hsl(var(--chart-${(payload[0].payload.index % 3) + 1}))`
                                }}
                              ></div>
                              <p className="text-sm font-medium">
                                {payload[0].payload.source}
                              </p>
                            </div>
                            <p className="text-sm text-muted-foreground">
                              {payload[0].value} leads ({Math.round((payload[0].value as number) / (stats.totalLeads || 1) * 100)}%)
                            </p>
                          </div>
                        );
                      }
                      return null;
                    }}
                  />
                </PieChart>
              </ResponsiveContainer>
            </ChartContainer>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mt-2">
              <div className="border rounded-md p-3">
                <p className="text-sm font-medium mb-1">Top Source</p>
                <div className="flex items-center gap-2">
                  <div 
                    className="h-3 w-3 rounded-full" 
                    style={{ background: 'hsl(var(--chart-1))' }}
                  ></div>
                  <p className="text-base font-bold">{stats.leadsBySource[0]?.source || "N/A"}</p>
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  {stats.leadsBySource[0]?.count || 0} leads ({Math.round((stats.leadsBySource[0]?.count || 0) / (stats.totalLeads || 1) * 100)}% of total)
                </p>
              </div>

              <div className="border rounded-md p-3">
                <p className="text-sm font-medium mb-1">Source Diversity</p>
                <p className="text-base font-bold">{stats.leadsBySource.length} active sources</p>
                <p className="text-xs text-muted-foreground mt-1">
                  A diverse mix of lead sources improves your market coverage
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Call to Action Section */}
      <div className="mt-8 mb-4">
        <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-none overflow-hidden">
          <CardContent className="p-6 flex flex-col md:flex-row items-center gap-6">
            <div className="flex-1">
              <h3 className="text-lg font-medium mb-2">Ready to Generate More Leads?</h3>
              <p className="text-muted-foreground mb-4">
                Use our lead generation tools to discover new potential restaurant clients across Switzerland.
              </p>
              <div className="flex flex-col sm:flex-row gap-3">
                <Link href="/lead-generator">
                  <button className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors">
                    Generate New Leads
                  </button>
                </Link>
                <Link href="/leads">
                  <button className="px-4 py-2 bg-white border border-primary text-primary rounded-md hover:bg-primary/5 transition-colors">
                    View All Leads
                  </button>
                </Link>
              </div>
            </div>
            <div className="flex-shrink-0 grid grid-cols-2 gap-3 max-w-[250px]">
              <div className="bg-white p-3 rounded-md shadow-sm flex flex-col items-center justify-center text-center aspect-square">
                <p className="text-xs text-muted-foreground">Monthly Growth</p>
                <p className="text-lg font-bold text-green-600">+{stats?.monthlyGrowth}%</p>
              </div>
              <div className="bg-white p-3 rounded-md shadow-sm flex flex-col items-center justify-center text-center aspect-square">
                <p className="text-xs text-muted-foreground">Cantons</p>
                <p className="text-lg font-bold">{stats?.cantonDistribution.length}/26</p>
              </div>
              <div className="bg-white p-3 rounded-md shadow-sm flex flex-col items-center justify-center text-center aspect-square">
                <p className="text-xs text-muted-foreground">Sources</p>
                <p className="text-lg font-bold">{stats?.leadsBySource.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
