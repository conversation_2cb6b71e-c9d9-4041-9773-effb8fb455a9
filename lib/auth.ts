import { cookies } from "next/headers";
import { getUserByUsername } from "@/lib/db";
import { User, UserSession, UserRole } from "@/types/user";
import crypto from "crypto";
import { NextAuthOptions } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import { neonConfig } from "@neondatabase/serverless";
import { compare } from "bcryptjs";
import PgAdapter from "@auth/pg-adapter";
import { Pool } from 'pg';

// We'll keep these for backward compatibility during transition
const AUTH_TRUE = "truee6f6fa20bec2e5a5ad7649667c9c";
const AUTH_FALSE = "false4ad1595cee236f7be3c2da5539fd";

// Define a type for the user data coming from the database
interface DbUser {
  id: string;
  name?: string | null;
  username?: string | null;
  email?: string | null; // Assuming email might be present
  emailVerified?: Date | null;
  image?: string | null;
  password?: string | null; // Include password
  role?: string;
}

// Set up neon config for production
if (process.env.NODE_ENV === 'production') {
  // @ts-expect-error - neonConfig types are not correctly defined
  neonConfig.wsProxy = true;
  neonConfig.useSecureWebSocket = true;
}

// Create a PostgreSQL pool for all database operations
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: {
    rejectUnauthorized: false, // Required for Neon
  },
});

// Define custom types for next-auth
declare module "next-auth" {
  interface Session {
    user: {
      id?: string;
      name?: string | null;
      username?: string | null;
      image?: string | null;
      role?: string;
    }
  }

  interface User {
    id: string;
    name?: string | null;
    username?: string | null;
    image?: string | null;
    role?: string;
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    id?: string;
    role?: string;
    username?: string | null;
  }
}

// Define the Auth.js configuration options
export const authOptions: NextAuthOptions = {
  adapter: PgAdapter(pool),
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        username: { label: "Username", type: "text" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.username || !credentials?.password) {
          return null;
        }

        try {
          // Get user from the database
          const result = await pool.query<DbUser>( // Use DbUser type here
            'SELECT * FROM users WHERE username = $1',
            [credentials.username]
          );

          const dbUser = result.rows[0]; // dbUser is now correctly typed as DbUser

          if (!dbUser || !dbUser.password) {
            return null;
          }

          // Compare passwords
          const passwordMatch = await compare(credentials.password, dbUser.password);

          if (!passwordMatch) {
            return null;
          }

          // Return user conforming to the next-auth User type (without password)
          return {
            id: dbUser.id,
            name: dbUser.name,
            username: dbUser.username,
            image: dbUser.image,
            role: dbUser.role
            // email is not included here as next-auth User type doesn't have it by default
          };
        } catch (error) {
          console.error("Authentication error:", error);
          return null;
        }
      }
    })
  ],

  session: {
    strategy: "jwt",
    maxAge: 24 * 60 * 60, // 1 day
  },

  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
        token.role = user.role;
        token.username = user.username || null;
      }
      return token;
    },

    async session({ session, token }) {
      if (session.user) {
        session.user.id = token.id;
        session.user.role = token.role;
        session.user.username = token.username;
      }
      return session;
    }
  },

  pages: {
    signIn: "/login",
    error: "/login",
    signOut: "/login"
  },

  debug: process.env.NODE_ENV === "development",
};

// New authentication function that works with user credentials
export async function authenticateUser(username: string, password: string): Promise<UserSession | null> {
  try {
    // Assume getUserByUsername fetches the necessary fields including password
    const user = await getUserByUsername(username) as DbUser | null;

    // We need to use compare to check the password
    if (!user || !user.password) { // Check if user and password hash exist
      console.log("Authentication failed: User not found or password not set.");
      return null;
    }

    const passwordMatch = await compare(password, user.password);

    if (!passwordMatch) {
      console.log("Authentication failed: Password mismatch.");
      return null;
    }

    // Generate a unique session ID
    const sessionId = crypto.randomUUID();

    const userSession: UserSession = {
      id: user.id,
      username: user.username,
      role: user.role as UserRole | undefined,
      sessionId: sessionId,
      createdAt: new Date().toISOString()
    };

    // Set session cookie
    const cookieStore = await cookies();
    cookieStore.set("user_session", JSON.stringify(userSession), {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      maxAge: 60 * 60 * 24, // 1 day
    });

    return userSession;
  } catch (error) {
    console.error("Authentication error:", error);
    return null;
  }
}

// Legacy authentication function - will be deprecated
export async function authenticate(password: string) {
  if (password === process.env.ACCESS_TOKEN_SECRET) {
    const cookieStore = await cookies();
    cookieStore.set("auth", AUTH_TRUE, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      maxAge: 60 * 60 * 24, // 1 day
    });
    return AUTH_TRUE;
  }
  return AUTH_FALSE;
}

export async function getUserSession(): Promise<UserSession | null> {
  const cookieStore = await cookies();
  const sessionCookie = cookieStore.get("user_session");
  
  if (!sessionCookie) {
    return null;
  }
  
  try {
    return JSON.parse(sessionCookie.value) as UserSession;
  } catch {
    return null;
  }
}

export async function clearUserSession() {
  const cookieStore = await cookies();
  cookieStore.delete("user_session");
  cookieStore.delete("auth"); // Also clear legacy cookie
} 