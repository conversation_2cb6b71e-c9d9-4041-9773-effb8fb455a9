import { NextRequest, NextResponse } from "next/server";
import { sql } from "@vercel/postgres";
import { Lead } from "@/types/lead";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const leads: Lead[] = body.leads;

    if (!leads || !Array.isArray(leads) || leads.length === 0) {
      return NextResponse.json(
        { error: "No valid leads provided" },
        { status: 400 }
      );
    }

    let importedCount = 0;
    const errors: any[] = [];
    const leadIds: number[] = [];

    // Process each lead
    for (const lead of leads) {
      try {
        // Skip leads without essential data
        if (!lead.name || !lead.erp_id) {
          errors.push({
            lead,
            error: "Missing required fields (name or erp_id)",
          });
          continue;
        }

        // Check if lead with this ERP ID already exists
        const existingResult = await sql`
          SELECT id FROM bianchi_leads WHERE erp_id = ${lead.erp_id}
        `;

        if (existingResult.rows.length > 0) {
          // Update existing lead
          const id = existingResult.rows[0].id;

          await sql`
            UPDATE bianchi_leads
            SET
              name = ${lead.name},
              source = ${lead.source || "ERP Import"},
              street_name = ${lead.street_name},
              street_number = ${lead.street_number},
              city = ${lead.city},
              canton = ${lead.canton},
              country = ${lead.country || "CH"},
              postal_code = ${lead.postal_code},
              international_phone = ${lead.international_phone},
              national_phone = ${lead.national_phone},
              website_uri = ${lead.website_uri},
              formatted_address = ${lead.formatted_address},
              email = ${lead.email},
              approved = ${lead.approved},
              language_code = ${lead.language_code},
              language_description = ${lead.language_description},
              name1 = ${lead.name1},
              name2 = ${lead.name2},
              name3 = ${lead.name3},
              contact_person_email = ${lead.contact_person_email},
              address_group = ${lead.address_group},
              address_group_description = ${lead.address_group_description},
              representative1 = ${lead.representative1},
              representative2 = ${lead.representative2},
              parent_group = ${lead.parent_group},
              parent_group_description = ${lead.parent_group_description},
              "group" = ${lead.group},
              group_description = ${lead.group_description},
              business_type = ${lead.business_type},
              business_type_description = ${lead.business_type_description},
              salutation_number = ${lead.salutation_number},
              salutation_description = ${lead.salutation_description},
              contact_person_first_name = ${lead.contact_person_first_name},
              contact_person_last_name = ${lead.contact_person_last_name},
              updated_at = CURRENT_TIMESTAMP
            WHERE id = ${id}
          `;

          // Add the lead ID to the list of updated leads
          leadIds.push(id);
        } else {
          // Insert new lead
          const insertResult = await sql`
            INSERT INTO bianchi_leads (
              source,
              erp_id,
              name,
              street_name,
              street_number,
              city,
              canton,
              country,
              postal_code,
              international_phone,
              national_phone,
              website_uri,
              formatted_address,
              email,
              approved,
              language_code,
              language_description,
              name1,
              name2,
              name3,
              contact_person_email,
              address_group,
              address_group_description,
              representative1,
              representative2,
              parent_group,
              parent_group_description,
              "group",
              group_description,
              business_type,
              business_type_description,
              salutation_number,
              salutation_description,
              contact_person_first_name,
              contact_person_last_name,
              created_at,
              updated_at
            ) VALUES (
              ${lead.source || "ERP Import"},
              ${lead.erp_id},
              ${lead.name},
              ${lead.street_name},
              ${lead.street_number},
              ${lead.city},
              ${lead.canton},
              ${lead.country || "CH"},
              ${lead.postal_code},
              ${lead.international_phone},
              ${lead.national_phone},
              ${lead.website_uri},
              ${lead.formatted_address},
              ${lead.email},
              ${lead.approved},
              ${lead.language_code},
              ${lead.language_description},
              ${lead.name1},
              ${lead.name2},
              ${lead.name3},
              ${lead.contact_person_email},
              ${lead.address_group},
              ${lead.address_group_description},
              ${lead.representative1},
              ${lead.representative2},
              ${lead.parent_group},
              ${lead.parent_group_description},
              ${lead.group},
              ${lead.group_description},
              ${lead.business_type},
              ${lead.business_type_description},
              ${lead.salutation_number},
              ${lead.salutation_description},
              ${lead.contact_person_first_name},
              ${lead.contact_person_last_name},
              CURRENT_TIMESTAMP,
              CURRENT_TIMESTAMP
            )
            RETURNING id
          `;

          // Add the new lead ID to the list
          if (insertResult.rows.length > 0) {
            leadIds.push(insertResult.rows[0].id);
          }
        }

        importedCount++;
      } catch (error) {
        console.error("Error importing lead:", error);
        errors.push({
          lead,
          error: error instanceof Error ? error.message : "Unknown error",
        });
      }
    }

    return NextResponse.json({
      imported: importedCount,
      errors: errors.length > 0 ? errors : undefined,
      total: leads.length,
      leadIds: leadIds,
    });
  } catch (error) {
    console.error("Error in lead import API:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 }
    );
  }
}
