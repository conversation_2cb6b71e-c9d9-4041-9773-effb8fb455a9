"use client";

import * as React from "react";
import { cn } from "@/lib/utils";

interface FixedTableContainerProps
  extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

/**
 * A container for tables that keeps the horizontal scrollbar fixed at the bottom of the viewport
 * This approach uses a sticky container that stays at the bottom of the viewport
 * for the horizontal scrollbar, while the table content can be scrolled normally.
 */
const FixedTableContainer = React.forwardRef<
  HTMLDivElement,
  FixedTableContainerProps
>(({ className, children, ...props }, ref) => {
  return (
    <div className={cn("relative w-full", className)} {...props} ref={ref}>
      {/* Use a sticky container for the horizontal scrollbar */}
      <div className="w-full overflow-x-auto sticky bottom-0 bg-white border-t z-10">
        {children}
      </div>
    </div>
  );
});

FixedTableContainer.displayName = "FixedTableContainer";

export { FixedTableContainer };
