"use client";

import React, { useState, useEffect, useRef } from "react";
import { Card } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import { Eye, EyeOff, Database, RefreshCw } from "lucide-react";

interface ApiProgressDisplayProps {
  progress: number;
  apiCallsTotal: number;
  apiCallsCompleted: number;
  apiCallsFailed: number;
  apiCallsInProgress: number;
  showDebugPanel: boolean;
  toggleDebugPanel: () => void;
  savedState: any | null;
}

const ApiProgressDisplay: React.FC<ApiProgressDisplayProps> = ({
  progress,
  apiCallsTotal,
  apiCallsCompleted,
  apiCallsFailed,
  apiCallsInProgress,
  showDebugPanel,
  toggleDebugPanel,
  savedState,
}) => {
  // Add local state to track animation
  const [animatedProgress, setAnimatedProgress] = useState(progress);

  // Track last update time and activity status
  const [lastUpdateTime, setLastUpdateTime] = useState<number>(Date.now());
  const [isActive, setIsActive] = useState<boolean>(false);

  // Reference to the previous progress value to detect changes
  const prevProgressRef = useRef<number>(progress);

  // Smoothly animate progress changes
  useEffect(() => {
    // Check if progress has actually changed from previous value
    if (progress !== prevProgressRef.current) {
      // Update last update time and set active state
      setLastUpdateTime(Date.now());
      setIsActive(true);

      // Log progress update for debugging
      console.log(
        `Progress updated: ${progress}%, Completed: ${apiCallsCompleted}/${apiCallsTotal}`
      );

      // Update the reference
      prevProgressRef.current = progress;
    }

    // If progress has changed from animated value, update animation
    if (progress !== animatedProgress) {
      // For small changes, update immediately
      if (Math.abs(progress - animatedProgress) < 2) {
        setAnimatedProgress(progress);
      } else {
        // For larger changes, animate smoothly
        const interval = setInterval(() => {
          setAnimatedProgress((prev) => {
            const diff = progress - prev;
            const step =
              diff > 0
                ? Math.max(1, Math.ceil(diff / 10))
                : Math.min(-1, Math.floor(diff / 10));
            const next = prev + step;

            // If we're close enough, just set to the target value
            if (Math.abs(next - progress) < 1) {
              clearInterval(interval);
              return progress;
            }
            return next;
          });
        }, 50);

        return () => clearInterval(interval);
      }
    }
  }, [progress, animatedProgress, apiCallsCompleted, apiCallsTotal]);

  // Check if updates have stopped
  useEffect(() => {
    const checkActivityInterval = setInterval(() => {
      // If no update in 3 seconds, consider inactive
      if (Date.now() - lastUpdateTime > 3000) {
        setIsActive(false);
      }
    }, 1000);

    return () => clearInterval(checkActivityInterval);
  }, [lastUpdateTime]);

  // Format the saved state for display
  const formatSavedState = () => {
    if (!savedState) return "No saved state";

    try {
      // Create a simplified version for display
      const simplified = {
        timestamp: new Date(savedState.timestamp).toLocaleString(),
        points: savedState.points.length,
        results: savedState.partialResults.length,
        progress: savedState.searchProgress + "%",
        type: savedState.searchType,
        keyword: savedState.searchKeyword || "none",
      };

      return JSON.stringify(simplified, null, 2);
    } catch (error) {
      return "Error formatting saved state";
    }
  };

  return (
    <Card className="p-4 bg-slate-50 border-slate-200 mb-4">
      <div className="flex justify-between items-center mb-2">
        <h3 className="text-sm font-medium">API Progress</h3>
        <Button
          variant="ghost"
          size="sm"
          onClick={toggleDebugPanel}
          className="h-8 w-8 p-0"
        >
          {showDebugPanel ? (
            <EyeOff className="h-4 w-4" />
          ) : (
            <Eye className="h-4 w-4" />
          )}
          <span className="sr-only">
            {showDebugPanel ? "Hide Debug Info" : "Show Debug Info"}
          </span>
        </Button>
      </div>

      <div className="space-y-2">
        <div className="flex justify-between text-xs text-slate-500 mb-1">
          <span>Search Progress</span>
          <div className="flex items-center">
            <span>{animatedProgress}%</span>
            {(apiCallsInProgress > 0 || isActive) && (
              <span
                title={isActive ? "Actively receiving updates" : "In progress"}
              >
                <RefreshCw
                  className={`ml-1.5 h-3 w-3 animate-spin ${
                    isActive ? "text-green-500" : "text-blue-500"
                  }`}
                />
              </span>
            )}
          </div>
        </div>
        <Progress value={animatedProgress} className="h-2.5 mb-1" />
        <div className="text-xs text-slate-500 text-right">
          {apiCallsCompleted} of {apiCallsTotal} points processed
          {apiCallsInProgress > 0 && ` (${apiCallsInProgress} in progress)`}
        </div>

        <div className="grid grid-cols-4 gap-2 mt-3">
          <div className="bg-blue-50 p-2 rounded text-center">
            <div className="text-xs text-slate-500">Total</div>
            <div className="font-medium">{apiCallsTotal}</div>
          </div>
          <div className="bg-green-50 p-2 rounded text-center">
            <div className="text-xs text-slate-500">Completed</div>
            <div className="font-medium text-green-600">
              {apiCallsCompleted}
            </div>
          </div>
          <div className="bg-yellow-50 p-2 rounded text-center">
            <div className="text-xs text-slate-500">In Progress</div>
            <div className="font-medium text-yellow-600">
              {apiCallsInProgress}
            </div>
          </div>
          <div className="bg-red-50 p-2 rounded text-center">
            <div className="text-xs text-slate-500">Failed</div>
            <div className="font-medium text-red-600">{apiCallsFailed}</div>
          </div>
        </div>
      </div>

      {showDebugPanel && (
        <div className="mt-4 border-t pt-3">
          <div className="flex items-center mb-2">
            <Database className="h-4 w-4 mr-1.5 text-slate-400" />
            <h4 className="text-sm font-medium">LocalStorage Data</h4>
          </div>
          <pre className="text-xs bg-slate-100 p-2 rounded overflow-auto max-h-32 whitespace-pre-wrap">
            {formatSavedState()}
          </pre>
        </div>
      )}
    </Card>
  );
};

export default ApiProgressDisplay;
