import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { query } from "@/lib/db";
import { cacheManager } from "@/lib/cache";

// Cache duplicate information for 10 minutes
const DUPLICATES_CACHE_KEY = "leads:duplicates";
const DUPLICATES_TTL = 10 * 60 * 1000; // 10 minutes

interface DuplicateGroup {
  google_place_id: string;
  count: number;
  lead_ids: string[];
}

interface DuplicatesInfo {
  duplicateGroups: DuplicateGroup[];
  totalDuplicateLeads: number;
  duplicateGroupsCount: number;
}

async function getDuplicatesInfo(): Promise<DuplicatesInfo> {
  // Check cache first
  const cached = cacheManager.get<DuplicatesInfo>(DUPLICATES_CACHE_KEY);
  if (cached && !cacheManager.isStale(DUPLICATES_CACHE_KEY)) {
    return cached;
  }

  // Find all leads with duplicate Google Place IDs
  const duplicatesQuery = `
    SELECT 
      google_place_id,
      COUNT(*) as count,
      ARRAY_AGG(id ORDER BY created_at) as lead_ids
    FROM "bianchi_leads"
    WHERE 
      country = 'Switzerland' 
      AND google_place_id IS NOT NULL 
      AND google_place_id != '' 
      AND google_place_id != 'None'
    GROUP BY google_place_id
    HAVING COUNT(*) > 1
    ORDER BY COUNT(*) DESC, google_place_id
  `;

  const duplicateResults = await query(duplicatesQuery);

  const duplicateGroups: DuplicateGroup[] = duplicateResults.map(row => ({
    google_place_id: row.google_place_id,
    count: parseInt(row.count),
    lead_ids: row.lead_ids,
  }));

  const totalDuplicateLeads = duplicateGroups.reduce((sum, group) => sum + group.count, 0);
  const duplicateGroupsCount = duplicateGroups.length;

  const duplicatesInfo: DuplicatesInfo = {
    duplicateGroups,
    totalDuplicateLeads,
    duplicateGroupsCount,
  };

  // Cache the result
  cacheManager.set(DUPLICATES_CACHE_KEY, duplicatesInfo, DUPLICATES_TTL);

  return duplicatesInfo;
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    const duplicatesInfo = await getDuplicatesInfo();
    return NextResponse.json(duplicatesInfo);
  } catch (error) {
    console.error("Error fetching duplicates info:", error);
    return NextResponse.json(
      { error: "Failed to fetch duplicates info" },
      { status: 500 }
    );
  }
}
