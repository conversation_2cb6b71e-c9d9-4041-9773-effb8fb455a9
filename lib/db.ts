import { Pool } from "pg";
import { Lead } from "@/types/lead";
import { User, UserWithPassword } from "@/types/user";

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: {
    rejectUnauthorized: false, // Required for <PERSON><PERSON>
  },
});

export const query = async (text: string, params?: any[]) => {
  const client = await pool.connect();
  try {
    const res = await client.query(text, params);
    return res.rows;
  } finally {
    client.release();
  }
};

export async function getLeads() {
  try {
    const rows = await query(`
      SELECT id, name, canton, formatted_address, created_at
      FROM "bianchi_leads"
      ORDER BY created_at DESC;
    `);

    return rows.map((row) => ({
      id: row.id,
      name: row.name,
      canton: row.canton,
      formatted_address: row.formatted_address,
      created_at: new Date(row.created_at).toISOString(),
    }));
  } catch (error) {
    console.error("Database error:", error);
    throw error;
  }
}

export async function getUserByUsername(
  username: string
): Promise<UserWithPassword | null> {
  console.log("Fetching user by username:", username);
  try {
    const rows = await query(
      `SELECT id, username, password, role FROM "users" WHERE username = $1`,
      [username]
    );

    console.log("User query result:", rows);

    if (rows.length === 0) {
      console.log("No user found with username:", username);
      return null;
    }

    const user = {
      id: rows[0].id,
      username: rows[0].username,
      password: rows[0].password,
      role: rows[0].role,
    };

    console.log("User found:", { ...user, password: "[REDACTED]" });
    return user;
  } catch (error) {
    console.error("Error fetching user by username:", error);
    throw error;
  }
}

export async function getAllUsers(): Promise<UserWithPassword[]> {
  try {
    const rows = await query(
      `SELECT id, username, password, role FROM "users" ORDER BY username ASC`
    );

    return rows.map((row) => ({
      id: row.id,
      username: row.username,
      password: row.password,
      role: row.role,
    }));
  } catch (error) {
    console.error("Database error:", error);
    throw error;
  }
}

export async function createUser(
  user: Omit<UserWithPassword, "id">
): Promise<User> {
  try {
    const rows = await query(
      `INSERT INTO "users" (username, password, role)
       VALUES ($1, $2, $3)
       RETURNING id, username, password, role`,
      [user.username, user.password, user.role]
    );

    return rows[0];
  } catch (error) {
    console.error("Database error:", error);
    throw error;
  }
}

export async function updateUser(user: UserWithPassword): Promise<User> {
  try {
    const rows = await query(
      `UPDATE "users" SET username = $1, password = $2, role = $3
       WHERE id = $4
       RETURNING id, username, password, role`,
      [user.username, user.password, user.role, user.id]
    );

    return rows[0];
  } catch (error) {
    console.error("Database error:", error);
    throw error;
  }
}

export async function deleteUser(id: number): Promise<boolean> {
  try {
    const result = await query(`DELETE FROM "users" WHERE id = $1`, [id]);

    return result.length > 0;
  } catch (error) {
    console.error("Database error:", error);
    throw error;
  }
}
