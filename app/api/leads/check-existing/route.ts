import { NextRequest, NextResponse } from 'next/server';
import { sql } from '@vercel/postgres';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const erpIds: string[] = body.erpIds;

    // If no ERP IDs provided, return empty array
    if (!erpIds || !Array.isArray(erpIds) || erpIds.length === 0) {
      return NextResponse.json({ existingIds: [] });
    }

    // Create a parameterized IN clause for the SQL query
    // This approach avoids the ANY operator which was causing type issues
    const params = erpIds.map((_, i) => `$${i + 1}`).join(',');
    const query = `
      SELECT erp_id
      FROM bianchi_leads
      WHERE erp_id IN (${params})
    `;

    const result = await sql.query(query, erpIds);

    // Extract the existing ERP IDs from the result
    const existingIds = result.rows.map(row => row.erp_id);

    return NextResponse.json({
      existingIds,
      total: existingIds.length
    });

  } catch (error) {
    console.error('Error checking existing leads:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
} 