import { NextResponse } from "next/server";
import { areSimilar } from "@/lib/string-similarity";

const GOOGLE_MAPS_API_KEY = process.env.GOOGLE_MAPS_API_KEY;
const PLACES_API_URL = "https://places.googleapis.com/v1/places:searchText";

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { name, street_name, city, country } = body;

    // Construct search query using available information
    const searchQuery = [name, street_name, city, country]
      .filter(Boolean)
      .join(", ");

    if (!searchQuery) {
      return NextResponse.json({ place_id: "N/A", googleName: null });
    }

    const response = await fetch(PLACES_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Goog-Api-Key': GOOGLE_MAPS_API_KEY!,
        'X-Goog-FieldMask': 'places.id,places.displayName'
      },
      body: JSON.stringify({
        textQuery: searchQuery,
        maxResultCount: 1
      })
    });

    if (!response.ok) {
      console.error("Places API error:", await response.text());
      return NextResponse.json({ place_id: "N/A", googleName: null });
    }

    const data = await response.json();
    const place = data.places?.[0];
    const googleName = place?.displayName?.text;
    
    // Only return the place_id if the names are similar enough
    if (place && googleName && areSimilar(name, googleName)) {
      return NextResponse.json({ 
        place_id: place.id,
        googleName 
      });
    }

    return NextResponse.json({ 
      place_id: "N/A",
      googleName: googleName || null
    });
  } catch (error) {
    console.error("Error finding place ID:", error);
    return NextResponse.json({ place_id: "N/A", googleName: null });
  }
} 