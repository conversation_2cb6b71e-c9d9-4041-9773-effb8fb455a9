import { SidebarProvider } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/app-sidebar";
import { PageHeader } from "@/components/page-header";
import { getSession } from "@/lib/session";

export default async function MainLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await getSession();
  
  return (
    <SidebarProvider defaultOpen={true}>
      <div className="flex flex-col h-full w-full">
        <div className="flex flex-1">
          <AppSidebar />
          <main className="flex-1 min-w-0 w-full">
            <PageHeader session={session} />
            <div className="p-4 w-full">
              {children}
            </div>
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
}