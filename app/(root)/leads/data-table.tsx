"use client";

import {
  useState,
  useEffect,
  forwardRef,
  useImper<PERSON><PERSON>andle,
  Ref,
} from "react";
import {
  ArrowUpDown,
  ChevronDown,
  MoreHorizontal,
  Download,
  Eye,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  Star,
  Search,
  X,
} from "lucide-react";
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
  FilterFn,
  Row,
} from "@tanstack/react-table";
import { addDays, isAfter, parseISO } from "date-fns";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  TableWrapper,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuPortal,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { exportToExcel } from "./export-utils";
import { Lead } from "@/types/lead";

declare module "@tanstack/table-core" {
  interface FilterFns {
    timePeriod: FilterFn<Lead>;
    exists: FilterFn<Lead>;
  }
}

interface PaginationInfo {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
}

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  pagination?: PaginationInfo;
  onPaginationChange?: (page: number, pageSize: number) => void;
  onSearchChange?: (search: string) => void;
  loading?: boolean;
}

// Helper function to determine row background color based on address_group_description and duplicate status
const getRowColorClass = (row: any): string => {
  const addressGroupDescription = row.original.address_group_description;
  const isHiddenDuplicate = row.original.isHiddenDuplicate;

  // Special styling for hidden duplicates
  if (isHiddenDuplicate) {
    return "bg-gray-50 hover:bg-gray-100 border-l-4 border-l-orange-300 opacity-75";
  }

  if (addressGroupDescription === "Kunde") {
    return "bg-green-50 hover:bg-green-100";
  } else if (addressGroupDescription === "Werbekunde") {
    return "bg-red-50 hover:bg-red-100";
  } else if (!addressGroupDescription) {
    return "bg-blue-50 hover:bg-blue-100";
  }

  return ""; // Default - no special color
};

export interface DataTableRef {
  clearAllFilters: () => void;
  setFilter: (column: string, value: any) => void;
  hasFilter: (column: string) => boolean;
  getActiveFilters: () => { column: string; value: any }[];
}

export const DataTable = forwardRef<DataTableRef, DataTableProps<any, any>>(
  function DataTable<TData, TValue>(
    {
      columns,
      data,
      pagination,
      onPaginationChange,
      onSearchChange,
      loading = false,
    }: DataTableProps<TData, TValue>,
    ref: React.ForwardedRef<DataTableRef>
  ) {
    const [sorting, setSorting] = useState<SortingState>([]);
    const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
    const [pageIndex, setPageIndex] = useState<string>("");
    const [searchTerm, setSearchTerm] = useState<string>("");

    const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({
      // Hide more detailed fields by default
      google_place_id: false,
      types: false,
      location: false,
      plus_code: false,
      price_level: false,
      user_rating_count: false,

      // Hide ERP fields by default
      erp_id: false,
      language_code: false,
      language_description: false,
      name1: false,
      name2: false,
      name3: false,
      parent_group: false,
      parent_group_description: false,
      group: false,
      group_description: false,
      address_group: false,
      address_group_description: true, // Make visible by default to show why rows have different colors
      representative1: false,
      representative2: false,
      salutation_number: false,
      salutation_description: false,
      business_type: false,
      business_type_description: false,
      contact_person_email: false,
      contact_person_first_name: false,
      contact_person_last_name: false,
      email: false,
    });
    const [rowSelection, setRowSelection] = useState({});

    // Expose methods to parent component
    useImperativeHandle(ref, () => ({
      clearAllFilters: () => {
        setColumnFilters([]);
        setSearchTerm("");
      },
      setFilter: (column: string, value: any) => {
        if (!value || (typeof value === "string" && value === "all")) {
          // Remove the filter if it exists
          setColumnFilters((prev) =>
            prev.filter((filter) => filter.id !== column)
          );
          return;
        }

        // Check if the filter already exists
        const filterExists = columnFilters.some(
          (filter) => filter.id === column
        );

        if (filterExists) {
          // Update existing filter
          setColumnFilters((prev) =>
            prev.map((filter) =>
              filter.id === column ? { ...filter, value } : filter
            )
          );
        } else {
          // Add new filter
          setColumnFilters((prev) => [...prev, { id: column, value }]);
        }
      },
      hasFilter: (column: string) => {
        return columnFilters.some((filter) => filter.id === column);
      },
      getActiveFilters: () => {
        return columnFilters.map((filter) => ({
          column: filter.id,
          value: filter.value,
        }));
      },
    }));

    // Apply global search filter
    useEffect(() => {
      if (searchTerm) {
        table.getColumn("name")?.setFilterValue(searchTerm);
      }
    }, [searchTerm]);

    const table = useReactTable({
      data,
      columns,
      getCoreRowModel: getCoreRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getFilteredRowModel: getFilteredRowModel(),
      onSortingChange: setSorting,
      onColumnFiltersChange: setColumnFilters,
      onColumnVisibilityChange: setColumnVisibility,
      onRowSelectionChange: setRowSelection,
      // Server-side pagination configuration
      manualPagination: !!pagination,
      pageCount: pagination ? pagination.totalPages : -1,
      initialState: {
        pagination: {
          pageSize: pagination ? pagination.pageSize : 25,
          pageIndex: pagination ? pagination.page - 1 : 0,
        },
      },
      state: {
        sorting,
        columnFilters,
        columnVisibility,
        rowSelection,
        pagination: pagination
          ? {
              pageIndex: pagination.page - 1,
              pageSize: pagination.pageSize,
            }
          : undefined,
      },
      filterFns: {
        timePeriod: (row, columnId, value) => {
          if (!value || value === "all") return true;

          const days = parseInt(value);
          const cutoffDate = addDays(new Date(), -days); // This date is X days ago
          const rowDate = parseISO(row.getValue(columnId));

          // Return true if the row date is after (more recent than) the cutoff date
          return isAfter(rowDate, cutoffDate);
        },
        exists: (row: Row<Lead>, columnId: string, value: any) => {
          if (value !== "exists") return true;

          const cellValue = row.getValue(columnId);
          if (cellValue === null || cellValue === undefined) return false;

          // For strings, check if it's not empty
          if (typeof cellValue === "string") return cellValue.trim() !== "";

          // For arrays, check if it has elements
          if (Array.isArray(cellValue)) return cellValue.length > 0;

          // For objects (like location), check if it exists
          if (typeof cellValue === "object") return cellValue !== null;

          return true;
        },
      },
    });

    // Data is now filtered in the page component

    // Add this function to handle exports
    const handleExport = (exportType: "all" | "selected") => {
      let dataToExport;
      if (exportType === "all") {
        dataToExport = table
          .getFilteredRowModel()
          .rows.map((row) => row.original);
      } else {
        dataToExport = table
          .getFilteredSelectedRowModel()
          .rows.map((row) => row.original);
      }

      const filename = `leads-export-${new Date().toISOString().split("T")[0]}`;
      exportToExcel(dataToExport as any[], filename);
    };

    // Improved column preset functions
    const showAllColumns = () => {
      table.getAllColumns().forEach((column) => {
        column.toggleVisibility(true);
      });
    };

    const showBasicColumnsOnly = () => {
      // First hide all columns
      table.getAllColumns().forEach((column) => {
        column.toggleVisibility(false);
      });

      // Then show only the basic ones
      const basicColumns = [
        "select",
        "name",
        "street_name",
        "postal_code",
        "city",
        "canton",
        "business_status",
        "international_phone",
        "website_uri",
        "source",
        "created_at",
        "address_group_description", // Include this to show why rows have different colors
        "actions",
      ];

      table.getAllColumns().forEach((column) => {
        if (basicColumns.includes(column.id)) {
          column.toggleVisibility(true);
        }
      });
    };

    const showERPColumnsOnly = () => {
      // First hide all columns
      table.getAllColumns().forEach((column) => {
        column.toggleVisibility(false);
      });

      // Then show only the ERP ones
      const erpColumns = [
        "select",
        "name",
        "city",
        "approved",
        "erp_id",
        "email",
        "contact_person_email",
        "contact_person_first_name",
        "contact_person_last_name",
        "language_code",
        "language_description",
        "address_group",
        "address_group_description",
        "representative1",
        "representative2",
        "business_type",
        "business_type_description",
        "name1",
        "name2",
        "name3",
        "parent_group",
        "group",
        "salutation_description",
        "actions",
      ];

      table.getAllColumns().forEach((column) => {
        if (erpColumns.includes(column.id)) {
          column.toggleVisibility(true);
        }
      });
    };

    return (
      <div className="space-y-4">
        <div className="flex items-center gap-4 flex-wrap">
          <div className="relative w-[300px]">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search by names..."
              value={searchTerm}
              onChange={(event) => {
                const value = event.target.value;
                setSearchTerm(value);
                // Use server-side search if callback is provided
                if (onSearchChange) {
                  onSearchChange(value);
                } else {
                  // Fallback to client-side search
                  table.getColumn("name")?.setFilterValue(value);
                }
              }}
              className="pl-8 w-full"
            />
            {searchTerm && (
              <Button
                variant="ghost"
                onClick={() => {
                  setSearchTerm("");
                  if (onSearchChange) {
                    onSearchChange("");
                  } else {
                    table.getColumn("name")?.setFilterValue("");
                  }
                }}
                className="absolute right-0 top-0 h-full aspect-square p-0 flex items-center justify-center"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="ml-auto">
                <Eye className="mr-2 h-4 w-4" />
                Columns
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[220px]">
              <DropdownMenuLabel>Column Presets</DropdownMenuLabel>
              <DropdownMenuItem onClick={showAllColumns}>
                Show All Columns
              </DropdownMenuItem>
              <DropdownMenuItem onClick={showBasicColumnsOnly}>
                Show Basic Columns Only
              </DropdownMenuItem>
              <DropdownMenuItem onClick={showERPColumnsOnly}>
                Show ERP Data Focus
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuLabel>Toggle Individual Columns</DropdownMenuLabel>
              <DropdownMenuSub>
                <DropdownMenuSubTrigger>
                  <span>Basic Information</span>
                </DropdownMenuSubTrigger>
                <DropdownMenuPortal>
                  <DropdownMenuSubContent>
                    {table
                      .getAllColumns()
                      .filter(
                        (column) =>
                          column.getCanHide() &&
                          !column.id.includes("_person") &&
                          !column.id.includes("language") &&
                          !column.id.includes("group") &&
                          !column.id.includes("representative") &&
                          !column.id.includes("salutation") &&
                          !column.id.includes("business_type") &&
                          column.id !== "address_number"
                      )
                      .map((column) => {
                        return (
                          <DropdownMenuCheckboxItem
                            key={column.id}
                            className="capitalize"
                            checked={column.getIsVisible()}
                            onCheckedChange={(value) =>
                              column.toggleVisibility(!!value)
                            }
                          >
                            {column.id.replace(/_/g, " ")}
                          </DropdownMenuCheckboxItem>
                        );
                      })}
                  </DropdownMenuSubContent>
                </DropdownMenuPortal>
              </DropdownMenuSub>
              <DropdownMenuSub>
                <DropdownMenuSubTrigger>
                  <span>Contact Information</span>
                </DropdownMenuSubTrigger>
                <DropdownMenuPortal>
                  <DropdownMenuSubContent>
                    {table
                      .getAllColumns()
                      .filter(
                        (column) =>
                          column.getCanHide() &&
                          (column.id.includes("email") ||
                            column.id.includes("_person") ||
                            column.id.includes("phone") ||
                            column.id.includes("salutation"))
                      )
                      .map((column) => {
                        return (
                          <DropdownMenuCheckboxItem
                            key={column.id}
                            className="capitalize"
                            checked={column.getIsVisible()}
                            onCheckedChange={(value) =>
                              column.toggleVisibility(!!value)
                            }
                          >
                            {column.id.replace(/_/g, " ")}
                          </DropdownMenuCheckboxItem>
                        );
                      })}
                  </DropdownMenuSubContent>
                </DropdownMenuPortal>
              </DropdownMenuSub>
              <DropdownMenuSub>
                <DropdownMenuSubTrigger>
                  <span>ERP Fields</span>
                </DropdownMenuSubTrigger>
                <DropdownMenuPortal>
                  <DropdownMenuSubContent>
                    {table
                      .getAllColumns()
                      .filter(
                        (column) =>
                          column.getCanHide() &&
                          (column.id.includes("group") ||
                            column.id.includes("representative") ||
                            column.id.includes("business_type") ||
                            column.id.includes("language") ||
                            column.id === "address_number" ||
                            column.id === "approved")
                      )
                      .map((column) => {
                        return (
                          <DropdownMenuCheckboxItem
                            key={column.id}
                            className="capitalize"
                            checked={column.getIsVisible()}
                            onCheckedChange={(value) =>
                              column.toggleVisibility(!!value)
                            }
                          >
                            {column.id.replace(/_/g, " ")}
                          </DropdownMenuCheckboxItem>
                        );
                      })}
                  </DropdownMenuSubContent>
                </DropdownMenuPortal>
              </DropdownMenuSub>
            </DropdownMenuContent>
          </DropdownMenu>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon">
                <Download className="h-4 w-4" />
                <span className="sr-only">Export data</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => handleExport("all")}>
                Export All Filtered
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => handleExport("selected")}
                disabled={table.getFilteredSelectedRowModel().rows.length === 0}
              >
                Export Selected
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <TableWrapper>
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && "selected"}
                    className={getRowColorClass(row)}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="h-24 text-center"
                  >
                    No results.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableWrapper>

        <div className="flex flex-col lg:flex-row items-center justify-between gap-4 py-4 border-t">
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2">
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">
                Rows per page:
              </span>
              <Select
                value={`${
                  pagination
                    ? pagination.pageSize
                    : table.getState().pagination.pageSize
                }`}
                onValueChange={(value) => {
                  const newPageSize = Number(value);
                  if (pagination && onPaginationChange) {
                    onPaginationChange(1, newPageSize); // Reset to page 1 when changing page size
                  } else {
                    table.setPageSize(newPageSize);
                  }
                }}
              >
                <SelectTrigger className="h-8 w-[70px]">
                  <SelectValue
                    placeholder={table.getState().pagination.pageSize}
                  />
                </SelectTrigger>
                <SelectContent side="top">
                  {[10, 25, 50, 100].map((pageSize) => (
                    <SelectItem key={pageSize} value={`${pageSize}`}>
                      {pageSize}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="text-sm text-muted-foreground">
              {pagination ? (
                <>
                  Showing {(pagination.page - 1) * pagination.pageSize + 1}-
                  {Math.min(
                    pagination.page * pagination.pageSize,
                    pagination.total
                  )}{" "}
                  of {pagination.total} entries
                </>
              ) : (
                <>
                  Showing{" "}
                  {table.getState().pagination.pageIndex *
                    table.getState().pagination.pageSize +
                    1}
                  -
                  {Math.min(
                    (table.getState().pagination.pageIndex + 1) *
                      table.getState().pagination.pageSize,
                    table.getFilteredRowModel().rows.length
                  )}{" "}
                  of {table.getFilteredRowModel().rows.length} entries
                </>
              )}
            </div>
          </div>

          <div className="text-sm text-muted-foreground">
            {table.getFilteredSelectedRowModel().rows.length} of{" "}
            {table.getFilteredRowModel().rows.length} row(s) selected.
          </div>

          <div className="flex items-center space-x-2">
            <div className="flex items-center gap-1 text-sm font-medium">
              <div className="flex items-center gap-1">
                <span>Page</span>
                <strong>
                  {pagination
                    ? pagination.page
                    : table.getState().pagination.pageIndex + 1}
                </strong>
                <span>of</span>
                <strong>
                  {pagination ? pagination.totalPages : table.getPageCount()}
                </strong>
              </div>
              <div className="flex items-center gap-1 ml-2">
                <Input
                  className="h-8 w-[50px]"
                  value={pageIndex}
                  onChange={(e) => setPageIndex(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      const page = parseInt(pageIndex);
                      if (
                        !isNaN(page) &&
                        page > 0 &&
                        page <= table.getPageCount()
                      ) {
                        table.setPageIndex(page - 1);
                        setPageIndex("");
                      }
                    }
                  }}
                  placeholder="#"
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const page = parseInt(pageIndex);
                    const maxPage = pagination
                      ? pagination.totalPages
                      : table.getPageCount();
                    if (!isNaN(page) && page > 0 && page <= maxPage) {
                      if (pagination && onPaginationChange) {
                        onPaginationChange(page, pagination.pageSize);
                      } else {
                        table.setPageIndex(page - 1);
                      }
                      setPageIndex("");
                    }
                  }}
                  disabled={!pageIndex}
                >
                  Go
                </Button>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="icon"
                onClick={() => {
                  if (pagination && onPaginationChange) {
                    onPaginationChange(1, pagination.pageSize);
                  } else {
                    table.setPageIndex(0);
                  }
                }}
                disabled={
                  pagination
                    ? pagination.page <= 1
                    : !table.getCanPreviousPage()
                }
                title="First Page"
              >
                <ChevronsLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={() => {
                  if (pagination && onPaginationChange) {
                    onPaginationChange(
                      pagination.page - 1,
                      pagination.pageSize
                    );
                  } else {
                    table.previousPage();
                  }
                }}
                disabled={
                  pagination
                    ? pagination.page <= 1
                    : !table.getCanPreviousPage()
                }
                title="Previous Page"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={() => {
                  if (pagination && onPaginationChange) {
                    onPaginationChange(
                      pagination.page + 1,
                      pagination.pageSize
                    );
                  } else {
                    table.nextPage();
                  }
                }}
                disabled={
                  pagination
                    ? pagination.page >= pagination.totalPages
                    : !table.getCanNextPage()
                }
                title="Next Page"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={() => {
                  if (pagination && onPaginationChange) {
                    onPaginationChange(
                      pagination.totalPages,
                      pagination.pageSize
                    );
                  } else {
                    table.setPageIndex(table.getPageCount() - 1);
                  }
                }}
                disabled={
                  pagination
                    ? pagination.page >= pagination.totalPages
                    : !table.getCanNextPage()
                }
                title="Last Page"
              >
                <ChevronsRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }
);
