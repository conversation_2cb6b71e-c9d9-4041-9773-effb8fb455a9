import * as XLSX from 'xlsx';
import { Lead } from '@/types/lead';

export const exportToExcel = (data: Lead[], filename: string) => {
  // Prepare data for export by flattening the location object
  const exportData = data.map(lead => ({
    ...lead,
    latitude: lead.location?.latitude,
    longitude: lead.location?.longitude,
    types: lead.types?.join(', '),
    created_at: new Date(lead.created_at).toLocaleDateString(),
    location: undefined
  }));

  const worksheet = XLSX.utils.json_to_sheet(exportData);
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Leads');
  XLSX.writeFile(workbook, `${filename}.xlsx`);
}; 