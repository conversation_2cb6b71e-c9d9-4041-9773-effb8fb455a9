import { NextResponse } from "next/server";

const GOOGLE_MAPS_API_KEY = process.env.GOOGLE_MAPS_API_KEY;
const GEOCODING_API_URL = "https://maps.googleapis.com/maps/api/geocode/json";
const PLACES_API_URL = "https://maps.googleapis.com/maps/api/place/nearbysearch/json";

export async function GET(request: Request) {
  try {
    // Log the full incoming request URL
    console.log("Incoming legacy search request URL:", request.url);

    const { searchParams } = new URL(request.url);
    const location = searchParams.get("location");
    const radius = searchParams.get("radius");
    const type = searchParams.get("type");
    const keyword = searchParams.get("keyword");
    const maxResults = searchParams.get("maxResults") || "20";

    // Log all environment variables (except sensitive values)
    console.log("Environment check:", {
      hasApiKey: !!GOOGLE_MAPS_API_KEY,
      keyLength: GOOGLE_MAPS_API_KEY?.length,
    });

    if (!location || !GOOGLE_MAPS_API_KEY) {
      console.log("Missing required parameters");
      return NextResponse.json(
        { error: "Missing required parameters" },
        { status: 400 }
      );
    }

    // Check if location is already in lat,lng format
    let lat: number, lng: number;
    if (location.includes(",")) {
      const [latStr, lngStr] = location.split(",");
      lat = parseFloat(latStr.trim());
      lng = parseFloat(lngStr.trim());
      
      if (isNaN(lat) || isNaN(lng)) {
        // If parsing fails, use geocoding
        const coordinates = await geocodeLocation(location);
        lat = coordinates.lat;
        lng = coordinates.lng;
      } else {
        console.log("Using provided coordinates:", { lat, lng });
      }
    } else {
      // Use geocoding to get coordinates
      const coordinates = await geocodeLocation(location);
      lat = coordinates.lat;
      lng = coordinates.lng;
    }

    // Prepare for pagination if needed
    const maxResultsNum = parseInt(maxResults);
    const results = await fetchPlacesWithPagination(
      lat,
      lng,
      radius || "1000",
      type || "restaurant",
      keyword || "",
      maxResultsNum
    );

    return NextResponse.json({
      results,
      status: "OK",
      total: results.length,
    });
  } catch (error) {
    console.error("Legacy API error:", error);
    return NextResponse.json(
      { error: "Failed to fetch places: " + (error instanceof Error ? error.message : String(error)) },
      { status: 500 }
    );
  }
}

// Helper function to geocode a location string to coordinates
async function geocodeLocation(location: string): Promise<{ lat: number; lng: number }> {
  const geocodeUrl = new URL(GEOCODING_API_URL);
  geocodeUrl.searchParams.append("address", location);
  geocodeUrl.searchParams.append("key", GOOGLE_MAPS_API_KEY!);

  console.log("Attempting geocoding request...");

  const geocodeResponse = await fetch(geocodeUrl.toString());
  const geocodeData = await geocodeResponse.json();

  console.log("Geocoding response status:", geocodeData.status);
  console.log("Geocoding response:", {
    hasResults: !!geocodeData.results?.length,
    status: geocodeData.status,
    error_message: geocodeData.error_message,
  });

  if (
    geocodeData.status !== "OK" ||
    !geocodeData.results?.[0]?.geometry?.location
  ) {
    throw new Error("Location not found: " + (geocodeData.error_message || geocodeData.status));
  }

  const { lat, lng } = geocodeData.results[0].geometry.location;
  console.log("Coordinates found:", { lat, lng });
  return { lat, lng };
}

// Helper function to fetch places with pagination
async function fetchPlacesWithPagination(
  lat: number,
  lng: number,
  radius: string,
  type: string,
  keyword: string,
  maxResults: number
): Promise<any[]> {
  let allResults: any[] = [];
  let nextPageToken: string | null = null;
  let pageCount = 0;
  const maxPages = Math.ceil(maxResults / 20); // Google returns max 20 results per page

  do {
    // Build the URL for the Places API
    const placesUrl = new URL(PLACES_API_URL);
    
    if (nextPageToken) {
      placesUrl.searchParams.append("pagetoken", nextPageToken);
    } else {
      // Only add these parameters on the first request
      placesUrl.searchParams.append("location", `${lat},${lng}`);
      placesUrl.searchParams.append("radius", radius);
      placesUrl.searchParams.append("type", type);
      if (keyword) {
        placesUrl.searchParams.append("keyword", keyword);
      }
    }
    
    placesUrl.searchParams.append("key", GOOGLE_MAPS_API_KEY!);
    
    console.log(`Fetching places page ${pageCount + 1}...`);
    
    // Need to wait a bit if using a page token (Google API requirement)
    if (nextPageToken) {
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    const placesResponse = await fetch(placesUrl.toString());
    
    if (!placesResponse.ok) {
      const errorText = await placesResponse.text();
      throw new Error(`Places API error: ${placesResponse.status} ${errorText}`);
    }
    
    const placesData = await placesResponse.json();
    
    if (placesData.status !== "OK" && placesData.status !== "ZERO_RESULTS") {
      throw new Error(`Places API returned status: ${placesData.status}`);
    }
    
    // Transform the results to match our expected format
    const pageResults = (placesData.results || []).map((place: any) => {
      // Extract address components if available
      const addressComponents = extractAddressComponents(place);
      
      return {
        place_id: place.place_id || `place_${Math.random()}`,
        name: place.name || "Unknown",
        formatted_address: place.vicinity || "",
        business_status: place.business_status || "UNKNOWN",
        location: {
          latitude: place.geometry?.location?.lat || null,
          longitude: place.geometry?.location?.lng || null,
        },
        types: place.types || [],
        // Add extracted address fields
        street_name: addressComponents.street_name,
        street_number: addressComponents.street_number,
        street_address: addressComponents.street_address,
        city: addressComponents.city,
        postal_code: addressComponents.postal_code,
        canton: addressComponents.canton,
        country: addressComponents.country,
        primary_type: place.types?.[0] || "",
        rating: place.rating,
        user_ratings_total: place.user_ratings_total,
      };
    });
    
    allResults = [...allResults, ...pageResults];
    nextPageToken = placesData.next_page_token || null;
    pageCount++;
    
    console.log(`Fetched ${pageResults.length} places. Total so far: ${allResults.length}`);
    
    // Stop if we've reached the maximum number of results or pages
    if (allResults.length >= maxResults || pageCount >= maxPages || !nextPageToken) {
      break;
    }
  } while (nextPageToken);
  
  // Trim to the requested maximum
  return allResults.slice(0, maxResults);
}

// Helper function to extract address components from a place
function extractAddressComponents(place: any): any {
  // Legacy API doesn't provide detailed address components
  // We'll return a simplified structure
  return {
    street_name: "",
    street_number: "",
    street_address: place.vicinity || "",
    city: "",
    postal_code: "",
    canton: "",
    country: "",
  };
}
