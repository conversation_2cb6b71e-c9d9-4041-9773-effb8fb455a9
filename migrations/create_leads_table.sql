CREATE TABLE IF NOT EXISTS "bianchi_leads" (
  id SERIAL PRIMARY KEY,
  source VARCHAR(50) NOT NULL,
  google_place_id VARCHAR(255),
  erp_id VARCHAR(255) UNIQUE,
  name VARCHAR(255) NOT NULL,
  formatted_address VARCHAR(255),
  address_components JSONB,
  street_name VA<PERSON>HA<PERSON>(255),
  street_number VA<PERSON><PERSON><PERSON>(50),
  city VARCHAR(100),
  postal_code VARCHAR(20),
  canton VARCHAR(100),
  country VARCHAR(100),
  international_phone VARCHAR(50),
  national_phone VARCHAR(50),
  website_uri VARCHAR(255),
  business_status VARCHAR(50),
  price_level INTEGER,
  rating NUMERIC(3, 2),
  user_rating_count INTEGER,
  reviews JSONB,
  opening_hours JSONB,
  types TEXT[],
  location GEOGRAPHY(POINT, 4326),
  plus_code VARCHAR(50),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  approved BOOLEAN,
  language_code VA<PERSON>HA<PERSON>(20),
  language_description VARCHAR(100),
  name1 VARCHAR(255),
  name2 VARCHAR(255),
  name3 VARCHAR(255),
  email VARCHAR(255),
  contact_person_email VARCHAR(255),
  address_group VARCHAR(100),
  address_group_description VARCHAR(255),
  representative1 VARCHAR(100),
  representative2 VARCHAR(100),
  parent_group VARCHAR(100),
  parent_group_description VARCHAR(255),
  group VARCHAR(100),
  group_description VARCHAR(255),
  business_type VARCHAR(100),
  business_type_description VARCHAR(255),
  salutation_number VARCHAR(50),
  salutation_description VARCHAR(255),
  contact_person_first_name VARCHAR(100),
  contact_person_last_name VARCHAR(100)
);

-- Create indexes
CREATE UNIQUE INDEX IF NOT EXISTS bianchi_leads_pkey ON "bianchi_leads" USING BTREE (id);
CREATE UNIQUE INDEX IF NOT EXISTS bianchi_leads_erp_id_key ON "bianchi_leads" USING BTREE (erp_id);
CREATE INDEX IF NOT EXISTS bianchi_leads_name_idx ON "bianchi_leads" USING BTREE (name);
CREATE INDEX IF NOT EXISTS bianchi_leads_canton_idx ON "bianchi_leads" USING BTREE (canton);
CREATE INDEX IF NOT EXISTS bianchi_leads_google_place_id_idx ON "bianchi_leads" USING BTREE (google_place_id);