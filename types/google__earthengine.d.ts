declare module '@google/earthengine' {
  export namespace data {
    function authenticateViaPrivateKey(
      privateKey: any,
      success: () => void,
      error: (err: any) => void
    ): void;
  }

  export function initialize(
    opt_baseurl: any,
    opt_tileurl: any,
    success?: () => void,
    error?: (err: any) => void
  ): void;

  export class ImageCollection {
    filterDate(startDate: string, endDate: string): ImageCollection;
    first(): Image;
    select(band: string): ImageCollection;
  }

  export class Image {
    select(band: string): Image;
    sample(point: Geometry, scale: number): any;
  }

  export class Geometry {
    static Point(coords: [number, number]): Geometry;
  }
} 