import { NextResponse } from "next/server";
import { query } from "@/lib/db";

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { leadId, placeId, googleName } = body;

    if (!leadId || !placeId) {
      return NextResponse.json(
        { error: "Lead ID and Place ID are required" },
        { status: 400 }
      );
    }

    // Update the lead with the Google Place ID
    const result = await query(
      `UPDATE "bianchi_leads" 
       SET google_place_id = $1, updated_at = CURRENT_TIMESTAMP
       WHERE id = $2
       RETURNING id, name, google_place_id`,
      [placeId, leadId]
    );

    if (result.length === 0) {
      return NextResponse.json(
        { error: "Lead not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      lead: result[0],
      message: `Updated lead ${result[0].name} with Google Place ID: ${placeId}`,
      googleName: googleName || null
    });
  } catch (error) {
    console.error("Failed to update lead with Google Place ID:", error);
    return NextResponse.json(
      {
        error: "Failed to update lead",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
