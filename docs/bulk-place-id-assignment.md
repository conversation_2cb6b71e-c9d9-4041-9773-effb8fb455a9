# Bulk Google Places ID Assignment

## Overview

The Bulk Google Places ID Assignment feature allows you to automatically find and assign Google Place IDs to leads that currently lack them. This feature uses the Google Places Text Search (New) API with the ID Only SKU to efficiently search for Place IDs while minimizing API costs.

## How to Access

1. Navigate to the **Leads** page
2. Click on the **Actions** dropdown menu
3. Select **Bulk Place ID Assignment**

Alternatively, you can access it directly at `/leads/bulk-place-id`

## Features

### Initial Statistics

- Shows the total number of leads without Google Place IDs
- Displays real-time counts of Place IDs found and selected for update

### Bulk Search Process

- **Cost-Optimized API Usage**: Uses Google Places Text Search API with ID Only SKU for minimal costs
- **Comprehensive Search Queries**: Constructs search queries using business name, full address, postal code, canton, and country
- **Location Bias**: Uses lead coordinates when available to improve search accuracy
- **Trust Google's Algorithm**: Relies on Google's superior search ranking to select the best match
- **Single Result Processing**: Requests only the most relevant result (maxResultCount: 1)
- **Rate Limiting**: Implements appropriate delays between API calls to avoid rate limits and ensure Vercel compatibility
- **Real-time Progress**: Shows live progress updates during the search process
- **Cancellation Support**: Stop button allows users to halt long-running searches at any time

### Search Quality & Confidence Levels

#### High Confidence

- Single result returned with location bias available
- Google's algorithm selected this as the best match with geographic context

#### Medium Confidence

- Single result returned without location bias
- Google's algorithm selected this as the best match based on text similarity alone

#### Failed

- No matches found by Google's search algorithm
- Insufficient data to construct search query
- API errors or rate limiting issues

### Review and Confirmation

- **Auto-selection**: All found Place IDs (both high and medium confidence) are automatically selected for update
- **Manual Review**: All results can be reviewed before database update
- **Google Maps Integration**: Click "View on Google Maps" links to verify Place IDs directly on Google Maps
- **Selective Update**: Choose which Place IDs to save to the database
- **Batch Operations**: Use "Select All Found" to select all results with Place IDs, or "Clear Selection" to deselect all

## API Usage and Costs

### Google Places API Configuration

- **Endpoint**: Google Places Text Search (New) API
- **SKU**: Text Search Essentials ID Only (cost-optimized)
- **Fields Requested**: `places.id`, `nextPageToken` (minimal fields for lowest cost)
- **Rate Limiting**: 100ms delay between requests (configurable)

### Search Strategy

1. **Primary Query**: Business name + full address + location bias
2. **Location Bias**: 1km radius around lead coordinates when available
3. **Single Result**: Requests only 1 result (maxResultCount: 1) to minimize costs
4. **Trust Google**: Relies on Google's algorithm to return the best match

## Technical Implementation

### API Endpoints

- `GET /api/leads/without-place-ids` - Retrieves leads lacking Place IDs
- `POST /api/leads/bulk-place-id-search` - Performs bulk search with streaming response
- `POST /api/leads/bulk-update-confirmed-place-ids` - Updates confirmed Place IDs

### Database Updates

- Only updates leads where `google_place_id IS NULL`
- Updates `google_place_id` field and `updated_at` timestamp
- Prevents overwriting existing Place IDs

### Error Handling

- Graceful handling of API rate limits and errors
- Detailed logging of failed searches with reasons
- Continues processing even if individual searches fail

## Best Practices

### Before Running

1. Ensure leads have complete address information for best results
2. Verify Google Places API key is configured and has sufficient quota
3. Consider running during off-peak hours for large datasets

### During Search

1. Monitor progress and error messages
2. Review confidence levels before confirming updates
3. Pay attention to failed searches and their reasons

### After Search

1. Review all auto-selected high confidence matches
2. Manually verify medium confidence matches if needed
3. Consider re-running for failed searches after improving lead data

## Troubleshooting

### Common Issues

- **No results found**: Lead data may be incomplete or business may not be in Google Places
- **Low confidence matches**: Business name may differ significantly from Google's records
- **API errors**: Check API key configuration and quota limits
- **Rate limiting**: Increase delay between requests if needed

### Performance Considerations

- Large datasets (1000+ leads) may take 10-20 minutes to complete
- Vercel function timeout limits apply (consider running locally for very large datasets)
- API quota consumption: approximately 1 request per lead

## Security and Privacy

- All searches use encrypted HTTPS connections
- No sensitive data is logged or stored beyond what's necessary
- Place IDs are only stored after explicit user confirmation
- Failed searches are logged with generic error messages only
