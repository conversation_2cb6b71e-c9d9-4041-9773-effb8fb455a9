import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { updateProgressData } from "../progress-utils";
// import { queryOverpass } from '@/lib/overpass'; // Commented out - file missing

interface SearchPoint {
  lat: number;
  lng: number;
  radius: number;
}

interface ExtensiveSearchRequest {
  points: SearchPoint[];
  type: string;
  keyword?: string;
  maxResultsPerPoint: number;
  includeResultsPerPoint?: boolean; // Flag to include results count per point
  batchSize?: number; // Number of points to process in each batch
  includeProgressUpdates?: boolean; // Flag to include progress updates in response
}

interface PlacesRequestBody {
  locationRestriction: {
    circle: {
      center: {
        latitude: number;
        longitude: number;
      };
      radius: number;
    };
  };
  includedTypes: string[];
  maxResultCount: number;
  languageCode: string;
  textQuery?: string;
}

interface PlaceResponse {
  id: string;
  displayName?: { text: string };
  formattedAddress?: string;
  businessStatus?: string;
  location?: { latitude: number; longitude: number };
  types?: string[];
  addressComponents?: any[];
  primaryTypeDisplayName?: { text: string };
  googleMapsUri?: string;
}

// Constants
const PLACES_API_URL = "https://places.googleapis.com/v1/places:searchNearby";

// Helper function to delay execution
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Parse request body
    const body: ExtensiveSearchRequest = await req.json();
    const {
      points,
      type,
      keyword,
      maxResultsPerPoint = 20,
      includeResultsPerPoint = false,
      batchSize = 10, // Default batch size of 10 points
      includeProgressUpdates = true, // Default to include progress updates
    } = body;

    console.log("=== EXTENSIVE SEARCH REQUEST ===");
    console.log(`Points count: ${points.length}`);
    console.log(`Type: ${type}`);
    console.log(`Keyword: ${keyword || "none"}`);
    console.log(`Max results per point: ${maxResultsPerPoint}`);
    console.log(`Include results per point: ${includeResultsPerPoint}`);
    console.log(`Batch size: ${batchSize}`);
    console.log(`Include progress updates: ${includeProgressUpdates}`);
    console.log("Sample points:", points.slice(0, 3));

    if (!points || !Array.isArray(points) || points.length === 0) {
      console.error("Invalid search points provided");
      return NextResponse.json(
        { error: "Invalid search points" },
        { status: 400 }
      );
    }

    if (!type) {
      console.error("Place type is required");
      return NextResponse.json(
        { error: "Place type is required" },
        { status: 400 }
      );
    }

    // Track unique results to avoid duplicates
    const uniqueResults = new Map();
    const failures = [];
    const resultsPerPoint = []; // Track results count per point
    const apiKey = process.env.GOOGLE_MAPS_API_KEY;

    if (!apiKey) {
      console.error("Google Maps API key not configured");
      return NextResponse.json(
        { error: "Google Maps API key not configured" },
        { status: 500 }
      );
    }

    console.log("Starting to process search points in batches...");

    // Process points in batches for better error handling and progress tracking
    const totalPoints = points.length;
    const totalBatches = Math.ceil(totalPoints / batchSize);

    // Set initial progress to 5% to show that something is happening
    updateProgressData(
      5, // progress
      Math.min(3, totalPoints), // in progress - estimate up to 3 concurrent requests
      0, // completed
      totalPoints, // total
      req.headers.get("x-search-id") || "default_search" // use header if provided or default
    );
    console.log(
      `Initialized search with ${totalPoints} points. Setting initial progress to 5%.`
    );

    // Process each batch of points
    for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
      const batchStart = batchIndex * batchSize;
      const batchEnd = Math.min(batchStart + batchSize, totalPoints);
      const currentBatch = points.slice(batchStart, batchEnd);

      console.log(
        `Processing batch ${batchIndex + 1}/${totalBatches} (points ${
          batchStart + 1
        }-${batchEnd})`
      );

      // Process each point in the current batch
      for (let j = 0; j < currentBatch.length; j++) {
        const pointIndex = batchStart + j;
        const point = currentBatch[j];
        console.log(
          `Processing point ${pointIndex + 1}/${totalPoints}: lat=${
            point.lat
          }, lng=${point.lng}, radius=${point.radius}m`
        );

        try {
          // Prepare request body for new Places API
          const requestBody: PlacesRequestBody = {
            locationRestriction: {
              circle: {
                center: {
                  latitude: point.lat,
                  longitude: point.lng,
                },
                radius: point.radius,
              },
            },
            includedTypes: [type],
            maxResultCount: maxResultsPerPoint,
            languageCode: "en",
          };

          // Add text query if keyword is provided
          if (keyword) {
            requestBody.textQuery = keyword;
          }

          console.log(
            `API Request for point ${pointIndex + 1}/${totalPoints}:`,
            JSON.stringify(requestBody)
          );

          // Make the request to Places API V1
          const response = await fetch(PLACES_API_URL, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "X-Goog-Api-Key": apiKey,
              "X-Goog-FieldMask": [
                "places.id",
                "places.displayName",
                "places.formattedAddress",
                "places.businessStatus",
                "places.location",
                "places.types",
                "places.googleMapsUri",
                "places.addressComponents",
                "places.primaryTypeDisplayName",
              ].join(","),
            },
            body: JSON.stringify(requestBody),
          });

          if (!response.ok) {
            const errorText = await response.text();
            console.error(
              `API error: ${response.status} ${response.statusText}`,
              errorText
            );
            failures.push({
              point,
              error: `API returned ${response.status}: ${response.statusText}. ${errorText}`,
            });
            continue;
          }

          const data = await response.json();
          console.log(
            `API Response status: ${response.status} ${response.statusText}`
          );

          // Track the number of results for this point
          const pointResultsCount = data.places?.length || 0;
          console.log(`Results count: ${pointResultsCount}`);

          // Store the results count for this point
          resultsPerPoint.push(pointResultsCount);

          // Process results, deduplicating by place_id
          if (data.places && Array.isArray(data.places)) {
            // Helper functions to extract address components
            function findAddressComponent(components: any[], type: string) {
              return components?.find((component) =>
                component.types.includes(type)
              );
            }

            function extractAddressData(addressComponents: any[]) {
              if (!addressComponents || !Array.isArray(addressComponents)) {
                return {
                  street_name: "",
                  city: "",
                  postal_code: "",
                  canton: "",
                  country: "Switzerland",
                };
              }

              // Get street components
              const streetName =
                findAddressComponent(addressComponents, "route")?.longText ||
                "";
              const city =
                findAddressComponent(addressComponents, "locality")?.longText ||
                "";
              const postalCode =
                findAddressComponent(addressComponents, "postal_code")
                  ?.longText || "";
              const canton =
                findAddressComponent(
                  addressComponents,
                  "administrative_area_level_1"
                )?.shortText || "";
              const country =
                findAddressComponent(addressComponents, "country")?.longText ||
                "Switzerland";

              return {
                street_name: streetName,
                city,
                postal_code: postalCode,
                canton,
                country,
              };
            }

            const initialSize = uniqueResults.size;
            data.places.forEach((place: PlaceResponse) => {
              if (!uniqueResults.has(place.id)) {
                const addressData = extractAddressData(
                  place.addressComponents || []
                );

                uniqueResults.set(place.id, {
                  place_id: place.id,
                  name: place.displayName?.text || "Unknown",
                  vicinity: place.formattedAddress?.split(",")[0] || "",
                  formatted_address: place.formattedAddress || "",
                  business_status: place.businessStatus || "UNKNOWN",
                  location: place.location
                    ? {
                        latitude: place.location.latitude || 0,
                        longitude: place.location.longitude || 0,
                      }
                    : null,
                  types: place.types || [],
                  maps_url:
                    place.googleMapsUri ||
                    `https://www.google.com/maps/place/?q=place_id:${place.id}`,

                  // Extract address components
                  street_name: addressData.street_name,
                  city: addressData.city,
                  postal_code: addressData.postal_code,
                  canton: addressData.canton,
                  country: addressData.country,
                  primary_type:
                    place.primaryTypeDisplayName?.text ||
                    place.types?.[0] ||
                    "unknown",
                });
              }
            });

            console.log(
              `Point ${pointIndex + 1}/${totalPoints} added ${
                uniqueResults.size - initialSize
              } results, total unique results: ${uniqueResults.size}`
            );
          }

          // Add a small delay between requests to avoid hitting API rate limits
          const isLastPointOverall = pointIndex === totalPoints - 1;

          if (!isLastPointOverall) {
            console.log("Adding delay before next point...");
            await delay(5);
          }
        } catch (error) {
          console.error(
            `Error processing point ${pointIndex + 1}/${totalPoints}:`,
            error
          );
          failures.push({
            point,
            error: error instanceof Error ? error.message : "Unknown error",
          });

          // Add a zero for failed points to maintain the correct array length
          resultsPerPoint.push(0);
        }

        // Calculate and log progress after each point
        const progress = Math.round(((pointIndex + 1) / totalPoints) * 100);
        console.log(
          `Progress: ${progress}% (${
            pointIndex + 1
          }/${totalPoints} points processed)`
        );

        // Update progress data in the server-side store
        updateProgressData(
          progress,
          Math.min(3, totalPoints - (pointIndex + 1)), // in progress - estimate up to 3 concurrent requests
          pointIndex + 1, // completed
          totalPoints, // total
          req.headers.get("x-search-id") || "default_search" // use header if provided or default
        );

        // Log progress update for debugging
        if (
          pointIndex % 5 === 0 ||
          pointIndex === 0 ||
          pointIndex === totalPoints - 1
        ) {
          console.log(
            `Updated server-side progress: ${progress}%, Completed: ${
              pointIndex + 1
            }/${totalPoints}`
          );
        }

        // Update localStorage with progress after each point for more frequent updates
        try {
          const storageKey = "precomputed_lead_search_state";
          const savedStateJson = globalThis.localStorage?.getItem(storageKey);

          if (savedStateJson) {
            const savedState = JSON.parse(savedStateJson);

            // Update progress
            savedState.searchProgress = progress;

            // Update partial results with current results
            savedState.partialResults = Array.from(uniqueResults.values());

            // Update results count per point
            savedState.resultsCountPerPoint = resultsPerPoint;

            // Save back to localStorage
            globalThis.localStorage?.setItem(
              storageKey,
              JSON.stringify(savedState)
            );

            // Log less frequently to avoid console spam
            if (pointIndex % 5 === 0) {
              console.log(
                `Updated localStorage with point progress: ${progress}%, Results: ${savedState.partialResults.length}`
              );
            }
          }
        } catch (error) {
          // Ignore localStorage errors - this is just for progress tracking
          console.log(
            "Error updating localStorage with point progress:",
            error
          );
        }
      }

      // After completing a batch, log batch summary
      const batchProgress = Math.round((batchEnd / totalPoints) * 100);
      console.log(
        `Batch ${
          batchIndex + 1
        }/${totalBatches} completed. Overall progress: ${batchProgress}%`
      );

      // Update progress data in the server-side store for the batch
      updateProgressData(
        batchProgress,
        Math.min(3, totalPoints - batchEnd), // in progress - estimate up to 3 concurrent requests
        batchEnd, // completed
        totalPoints, // total
        req.headers.get("x-search-id") || "default_search" // use header if provided or default
      );

      // Log batch progress update
      console.log(
        `Updated server-side batch progress: ${batchProgress}%, Batch ${
          batchIndex + 1
        }/${totalBatches}, Completed: ${batchEnd}/${totalPoints}`
      );

      // Save progress to localStorage for client-side tracking
      try {
        // Get current state from localStorage if it exists
        const storageKey = "precomputed_lead_search_state";
        const savedStateJson = globalThis.localStorage?.getItem(storageKey);

        if (savedStateJson) {
          const savedState = JSON.parse(savedStateJson);

          // Update progress and partial results
          savedState.searchProgress = batchProgress;

          // Update partial results with current results
          savedState.partialResults = Array.from(uniqueResults.values());

          // Update results count per point
          savedState.resultsCountPerPoint = resultsPerPoint;

          // Save back to localStorage
          globalThis.localStorage?.setItem(
            storageKey,
            JSON.stringify(savedState)
          );
          console.log(
            `Updated localStorage with progress: ${batchProgress}%, Results: ${savedState.partialResults.length}`
          );
        }
      } catch (error) {
        // Ignore localStorage errors - this is just for progress tracking
        console.log("Error updating localStorage with progress:", error);
      }
    }

    // Convert the Map to an array of results
    const results = Array.from(uniqueResults.values());

    console.log("=== EXTENSIVE SEARCH COMPLETED ===");
    console.log(`Total points processed: ${points.length}`);
    console.log(`Total unique results: ${results.length}`);
    console.log(`Failures: ${failures.length}`);

    if (failures.length > 0) {
      console.log("Sample failures:", failures.slice(0, 3));
    }

    if (results.length > 0) {
      console.log("Sample results:", results.slice(0, 3));
    }

    // Calculate statistics for results per point
    let maxResultsForPoint = 0;
    let avgResultsPerPoint = 0;

    if (resultsPerPoint.length > 0) {
      maxResultsForPoint = Math.max(...resultsPerPoint);
      avgResultsPerPoint =
        resultsPerPoint.reduce((sum, count) => sum + count, 0) /
        resultsPerPoint.length;

      console.log("=== RESULTS PER POINT STATISTICS ===");
      console.log(`Maximum results for any point: ${maxResultsForPoint}`);
      console.log(
        `Average results per point: ${avgResultsPerPoint.toFixed(2)}`
      );
    }

    // Update progress data to 100% complete
    updateProgressData(
      100, // progress
      0, // in progress
      totalPoints, // completed
      totalPoints, // total
      req.headers.get("x-search-id") || "default_search" // use header if provided or default
    );

    // Log final progress update
    console.log(
      `Updated server-side progress to 100% complete. Total points processed: ${totalPoints}`
    );

    // Prepare the response
    const response = {
      results,
      total: results.length,
      failures: failures.length > 0 ? failures : undefined,
      searchPointsCount: points.length,
      progress: 100, // Search is complete, so progress is 100%
    };

    // Include results per point statistics if requested
    if (includeResultsPerPoint) {
      Object.assign(response, {
        resultsPerPoint,
        maxResultsForPoint,
        avgResultsPerPoint,
      });
    }

    // Include detailed progress information if requested
    if (includeProgressUpdates) {
      Object.assign(response, {
        batchesProcessed: totalBatches,
        totalBatches: totalBatches,
        pointsProcessed: totalPoints,
        totalPoints: totalPoints,
        completionTime: new Date().toISOString(),
      });
    }

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error in extensive search:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 }
    );
  }
}
