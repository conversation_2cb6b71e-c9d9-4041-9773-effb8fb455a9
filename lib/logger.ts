"use client";

export enum LogAction {
  ERP_SYNC = "ERP_SYNC",
  LEAD_GENERATION = "LEAD_GENERATION",
  SETTINGS_UPDATE = "SETTINGS_UPDATE",
  LEAD_EXPORT = "LEAD_EXPORT",
  PLACE_ID_FETCH = "PLACE_ID_FETCH",
  LEAD_SAVE = "LEAD_SAVE",
  LEAD_UPDATE = "LEAD_UPDATE",
}

export async function logActivity(
  action: LogAction,
  description: string,
  metadata?: Record<string, any>
) {
  try {
    const response = await fetch("/api/logs", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        action,
        description,
        metadata: metadata || {},
      }),
    });

    if (!response.ok) {
      console.error("Failed to log activity:", await response.text());
    }
  } catch (error) {
    console.error("Error logging activity:", error);
  }
}
