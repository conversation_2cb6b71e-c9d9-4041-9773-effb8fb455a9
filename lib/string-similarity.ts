import { distance } from "fastest-levenshtein";

export function normalizedSimilarity(strA: string, strB: string) {
  const dist = distance(strA, strB);
  const maxLen = Math.max(strA.length, strB.length);
  // Handle edge case if both strings are empty
  if (maxLen === 0) return 1;
  return 1 - dist / maxLen;
}

export function areSimilar(
  strA: string,
  strB: string,
  similarityThreshold = 0.8,
  debug = false
) {
  // Normalize strings for comparison: lowercase and remove common business suffixes
  const normalize = (str: string) => {
    return (
      str
        .toLowerCase()
        .replace(/\s+/g, " ")
        // Enhanced business suffix removal for Swiss/German businesses
        .replace(
          /(restaurant|cafe|bar|gmbh|ag|ltd\.?|limited|inc\.?|bäckerei|konditorei|bakery|bistro|pizzeria|trattoria|osteria|hotel|pension|gasthof|gasthaus)$/i,
          ""
        )
        // Remove common German/Swiss business words
        .replace(/\b(und|&|and|et|e)\b/gi, "")
        // Remove punctuation and extra spaces
        .replace(/[.,\-_()]/g, " ")
        .replace(/\s+/g, " ")
        .trim()
    );
  };

  const normalizedA = normalize(strA);
  const normalizedB = normalize(strB);
  const sim = normalizedSimilarity(normalizedA, normalizedB);

  if (debug) {
    console.log(`    Original A: "${strA}"`);
    console.log(`    Original B: "${strB}"`);
    console.log(`    Normalized A: "${normalizedA}"`);
    console.log(`    Normalized B: "${normalizedB}"`);
    console.log(
      `    Similarity: ${sim.toFixed(3)} (threshold: ${similarityThreshold})`
    );
  }

  return sim >= similarityThreshold;
}
