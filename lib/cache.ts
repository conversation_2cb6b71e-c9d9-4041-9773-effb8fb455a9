import { LRUCache } from 'lru-cache';

// Cache configuration
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes
const CACHE_MAX_SIZE = 100; // Maximum number of cached entries
const STALE_WHILE_REVALIDATE_TTL = 10 * 60 * 1000; // 10 minutes for stale data

// Create LRU cache instance
const cache = new LRUCache<string, any>({
  max: CACHE_MAX_SIZE,
  ttl: CACHE_TTL,
  allowStale: true,
  updateAgeOnGet: true,
  updateAgeOnHas: true,
});

export interface CacheEntry<T = any> {
  data: T;
  timestamp: number;
  staleAt: number;
}

export class CacheManager {
  private static instance: CacheManager;
  private cache: LRUCache<string, CacheEntry>;

  private constructor() {
    this.cache = cache;
  }

  public static getInstance(): CacheManager {
    if (!CacheManager.instance) {
      CacheManager.instance = new CacheManager();
    }
    return CacheManager.instance;
  }

  /**
   * Get data from cache
   */
  get<T>(key: string): T | null {
    const entry = this.cache.get(key) as CacheEntry<T> | undefined;
    if (!entry) return null;

    const now = Date.now();
    
    // If data is fresh, return it
    if (now < entry.staleAt) {
      return entry.data;
    }

    // Data is stale but still in cache (stale-while-revalidate)
    return entry.data;
  }

  /**
   * Check if data is stale and needs revalidation
   */
  isStale(key: string): boolean {
    const entry = this.cache.get(key) as CacheEntry | undefined;
    if (!entry) return true;

    const now = Date.now();
    return now >= entry.staleAt;
  }

  /**
   * Set data in cache
   */
  set<T>(key: string, data: T, customTTL?: number): void {
    const now = Date.now();
    const ttl = customTTL || CACHE_TTL;
    
    const entry: CacheEntry<T> = {
      data,
      timestamp: now,
      staleAt: now + ttl,
    };

    this.cache.set(key, entry);
  }

  /**
   * Delete data from cache
   */
  delete(key: string): void {
    this.cache.delete(key);
  }

  /**
   * Clear all cache
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * Get cache statistics
   */
  getStats() {
    return {
      size: this.cache.size,
      max: this.cache.max,
      calculatedSize: this.cache.calculatedSize,
    };
  }

  /**
   * Generate cache key for leads query
   */
  static generateLeadsKey(params: {
    page?: number;
    pageSize?: number;
    search?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    filters?: Record<string, any>;
  }): string {
    const { page = 1, pageSize = 25, search = '', sortBy = 'created_at', sortOrder = 'desc', filters = {} } = params;
    
    // Create a stable key from parameters
    const filterKey = Object.keys(filters)
      .sort()
      .map(key => `${key}:${filters[key]}`)
      .join('|');
    
    return `leads:${page}:${pageSize}:${search}:${sortBy}:${sortOrder}:${filterKey}`;
  }

  /**
   * Generate cache key for leads count
   */
  static generateCountKey(params: {
    search?: string;
    filters?: Record<string, any>;
  }): string {
    const { search = '', filters = {} } = params;
    
    const filterKey = Object.keys(filters)
      .sort()
      .map(key => `${key}:${filters[key]}`)
      .join('|');
    
    return `leads:count:${search}:${filterKey}`;
  }

  /**
   * Invalidate all leads-related cache entries
   */
  invalidateLeadsCache(): void {
    const keys = Array.from(this.cache.keys());
    keys.forEach(key => {
      if (key.startsWith('leads:')) {
        this.cache.delete(key);
      }
    });
  }
}

// Export singleton instance
export const cacheManager = CacheManager.getInstance();

// Cache invalidation helpers
export const invalidateLeadsCache = () => {
  cacheManager.invalidateLeadsCache();
};

// Cache key generators
export const generateLeadsCacheKey = CacheManager.generateLeadsKey;
export const generateLeadsCountCacheKey = CacheManager.generateCountKey;
