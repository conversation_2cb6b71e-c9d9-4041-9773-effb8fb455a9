import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { UserSession } from "@/types/user";

export async function getSession() {
  const session = await getServerSession(authOptions);
  console.log("getSession helper - Session:", JSON.stringify(session, null, 2));
  return session;
}

export async function getCurrentUser() {
  const session = await getSession();
  console.log("getCurrentUser helper - User:", JSON.stringify(session?.user, null, 2));
  return session?.user;
}

// Check if the user has admin role
export async function isAdmin() {
  const session = await getSession();
  const userRole = session?.user?.role;
  
  console.log("isAdmin helper - User role:", userRole);
  console.log("isAdmin helper - Is admin:", userRole === 'admin');
  
  // Try multiple ways to check for admin role
  const isAdminExact = userRole === 'admin';
  const isAdminIncludes = userRole?.includes('admin');
  const isAdminLowerCase = userRole?.toLowerCase() === 'admin';
  
  console.log("isAdmin helper - Checks:", {
    isAdminExact,
    isAdminIncludes,
    isAdminLowerCase
  });
  
  // Return true if any of the checks pass
  return isAdminExact || isAdminIncludes || isAdminLowerCase;
}

// Check if the user is authenticated
export async function isAuthenticated() {
  const session = await getSession();
  console.log("isAuthenticated helper - Has session:", !!session);
  return !!session;
} 