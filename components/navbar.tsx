"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { ChevronDown, LogOut, Settings, User } from "lucide-react";
import { signOut, useSession } from "next-auth/react";

export function Navbar() {
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const router = useRouter();
  const { data: session, status } = useSession();
  const user = session?.user;
  
  // Force show management for debugging
  const [forceShowManagement, setForceShowManagement] = useState(false);
  
  useEffect(() => {
    // Log detailed session information
    console.log("**** NAVBAR SESSION DEBUG ****");
    console.log("Session status:", status);
    console.log("Full session object:", JSON.stringify(session, null, 2));
    console.log("User object:", JSON.stringify(user, null, 2));
    
    // Check role in various ways
    console.log("User role:", user?.role);
    console.log("User role type:", typeof user?.role);
    console.log("Is admin (===):", user?.role === "admin");
    console.log("Is admin (includes):", user?.role?.includes("admin"));
    console.log("Is admin (toLowerCase):", user?.role?.toLowerCase() === "admin");
    
    // After 2 seconds, force show the management component for debugging
    const timer = setTimeout(() => {
      setForceShowManagement(true);
      console.log("Force showing management component for debugging");
    }, 2000);
    
    return () => clearTimeout(timer);
  }, [session, status, user]);

  const handleLogout = async () => {
    try {
      setIsLoggingOut(true);
      await signOut({ redirect: false });
      router.push("/login");
      router.refresh();
    } catch (error) {
      console.error("Logout failed:", error);
    } finally {
      setIsLoggingOut(false);
    }
  };

  // Determine if user is admin through multiple checks
  const isAdmin = user?.role === "admin" || 
                 user?.role?.includes("admin") || 
                 user?.role?.toLowerCase() === "admin" ||
                 forceShowManagement;

  return (
    <nav className="bg-white border-b border-gray-200 py-4">
      <div className="container mx-auto px-4 flex justify-between items-center">
        <Link href="/" className="text-xl font-bold">
          Bianchi Lead App
        </Link>

        {user && (
          <div className="flex items-center gap-4">
            <span className="text-gray-700 hidden md:inline-block">
              Welcome, {user.name || "User"}
            </span>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="gap-1">
                  <User className="h-4 w-4" />
                  <span className="hidden md:inline-block">Account</span>
                  <ChevronDown className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>
                  {user.name || "User"} {user.role && `(${user.role})`}
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                {/* Show management link for admins or when forced for debugging */}
                {isAdmin && (
                  <>
                    <DropdownMenuItem asChild>
                      <Link href="/users" className="w-full">
                        <User className="h-4 w-4 mr-2" />
                        User Management
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                  </>
                )}
                <DropdownMenuItem onClick={handleLogout} disabled={isLoggingOut}>
                  <LogOut className="h-4 w-4 mr-2" />
                  {isLoggingOut ? "Logging out..." : "Logout"}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
      </div>
    </nav>
  );
} 