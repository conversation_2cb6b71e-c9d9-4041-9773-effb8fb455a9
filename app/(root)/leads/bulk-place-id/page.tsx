"use client";

import React, { useState, useEffect } from "react";

import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  AlertCircle,
  Loader2,
  MapPin,
  CheckCircle,
  XCircle,
  Search,
  Database,
  ArrowLeft,
  ExternalLink,
  X,
} from "lucide-react";
import Link from "next/link";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { logActivity, LogAction } from "@/lib/logger";

interface LeadWithoutPlaceId {
  id: number;
  name: string;
  formatted_address: string;
  street_name: string;
  street_number: string;
  city: string;
  postal_code: string;
  canton: string;
  country: string;
  international_phone: string | null;
  national_phone: string | null;
  location: {
    latitude: number;
    longitude: number;
  } | null;
}

interface PlaceIdSearchResult {
  leadId: number;
  leadName: string;
  placeId: string | null;
  googleName: string | null;
  confidence: "high" | "medium" | "failed";
  searchQuery: string;
  reason?: string;
}

interface SearchProgress {
  total: number;
  completed: number;
  successful: number;
  failed: number;
  inProgress: boolean;
}

export default function BulkPlaceIdPage() {
  const [leadsWithoutPlaceId, setLeadsWithoutPlaceId] = useState<
    LeadWithoutPlaceId[]
  >([]);
  const [searchResults, setSearchResults] = useState<PlaceIdSearchResult[]>([]);
  const [loading, setLoading] = useState(true);
  const [searching, setSearching] = useState(false);
  const [updating, setUpdating] = useState(false);
  const [progress, setProgress] = useState<SearchProgress>({
    total: 0,
    completed: 0,
    successful: 0,
    failed: 0,
    inProgress: false,
  });
  const [selectedResults, setSelectedResults] = useState<Set<number>>(
    new Set()
  );
  const [error, setError] = useState<string | null>(null);
  const [abortController, setAbortController] =
    useState<AbortController | null>(null);

  // Load leads without Place IDs on component mount
  useEffect(() => {
    loadLeadsWithoutPlaceId();
  }, []);

  const loadLeadsWithoutPlaceId = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch("/api/leads/without-place-ids");
      if (!response.ok) {
        throw new Error("Failed to load leads without Place IDs");
      }

      const data = await response.json();
      setLeadsWithoutPlaceId(data.leads);

      await logActivity(
        LogAction.LEAD_GENERATION,
        `Loaded ${data.leads.length} leads without Google Place IDs`,
        { count: data.leads.length }
      );
    } catch (error) {
      console.error("Error loading leads:", error);
      setError(error instanceof Error ? error.message : "Unknown error");
      toast.error("Failed to load leads without Place IDs");
    } finally {
      setLoading(false);
    }
  };

  const stopBulkSearch = () => {
    if (abortController) {
      abortController.abort();
      setAbortController(null);
      setSearching(false);
      setProgress((prev) => ({ ...prev, inProgress: false }));
      toast.info("Search stopped by user");
    }
  };

  const startBulkSearch = async () => {
    if (leadsWithoutPlaceId.length === 0) {
      toast.error("No leads available for search");
      return;
    }

    try {
      setSearching(true);
      setSearchResults([]);
      setSelectedResults(new Set());
      setProgress({
        total: leadsWithoutPlaceId.length,
        completed: 0,
        successful: 0,
        failed: 0,
        inProgress: true,
      });

      // Create abort controller for this search
      const controller = new AbortController();
      setAbortController(controller);

      const response = await fetch("/api/leads/bulk-place-id-search", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          leads: leadsWithoutPlaceId,
        }),
        signal: controller.signal,
      });

      if (!response.ok) {
        throw new Error("Failed to start bulk search");
      }

      // Process the streaming response
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error("No response body");
      }

      const decoder = new TextDecoder();
      let buffer = "";

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split("\n");
        buffer = lines.pop() || "";

        for (const line of lines) {
          if (line.trim()) {
            try {
              const data = JSON.parse(line);

              if (data.type === "progress") {
                setProgress((prev) => ({
                  ...prev,
                  completed: data.completed,
                  successful: data.successful,
                  failed: data.failed,
                }));
              } else if (data.type === "result") {
                setSearchResults((prev) => [...prev, data.result]);

                // Auto-select all results with valid Place IDs (excluding "None")
                if (data.result.placeId && data.result.placeId !== "None") {
                  setSelectedResults(
                    (prev) => new Set([...prev, data.result.leadId])
                  );
                }
              } else if (data.type === "complete") {
                setProgress((prev) => ({ ...prev, inProgress: false }));
                toast.success(
                  `Search completed! Found ${data.totalFound} Place IDs`
                );
              }
            } catch (e) {
              console.error("Error parsing response line:", line, e);
            }
          }
        }
      }

      await logActivity(
        LogAction.LEAD_GENERATION,
        `Completed bulk Place ID search for ${leadsWithoutPlaceId.length} leads`,
        {
          totalLeads: leadsWithoutPlaceId.length,
          successful: progress.successful,
          failed: progress.failed,
        }
      );
    } catch (error) {
      console.error("Error during bulk search:", error);

      // Handle abort signal differently
      if (error instanceof Error && error.name === "AbortError") {
        // Search was stopped by user, don't show error
        console.log("Search was aborted by user");
      } else {
        setError(error instanceof Error ? error.message : "Unknown error");
        toast.error("Failed to complete bulk search");
      }

      setProgress((prev) => ({ ...prev, inProgress: false }));
    } finally {
      setSearching(false);
      setAbortController(null);
    }
  };

  const updateSelectedPlaceIds = async () => {
    const selectedResultsData = searchResults.filter(
      (result) => selectedResults.has(result.leadId) && result.placeId
    );

    if (selectedResultsData.length === 0) {
      toast.error("No Place IDs selected for update");
      return;
    }

    try {
      setUpdating(true);

      const response = await fetch(
        "/api/leads/bulk-update-confirmed-place-ids",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            updates: selectedResultsData.map((result) => ({
              leadId: result.leadId,
              placeId: result.placeId,
              googleName: result.googleName,
            })),
          }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to update Place IDs");
      }

      const data = await response.json();

      toast.success(
        `Successfully updated ${data.updated} leads with Google Place IDs`
      );

      await logActivity(
        LogAction.LEAD_GENERATION,
        `Updated ${data.updated} leads with confirmed Google Place IDs`,
        { updatedCount: data.updated }
      );

      // Refresh the leads list
      await loadLeadsWithoutPlaceId();

      // Clear search results
      setSearchResults([]);
      setSelectedResults(new Set());
      setProgress({
        total: 0,
        completed: 0,
        successful: 0,
        failed: 0,
        inProgress: false,
      });
    } catch (error) {
      console.error("Error updating Place IDs:", error);
      toast.error("Failed to update Place IDs");
    } finally {
      setUpdating(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="flex items-center space-x-2">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span>Loading leads without Place IDs...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/leads">
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Leads
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold">
                Bulk Google Places ID Assignment
              </h1>
              <p className="text-muted-foreground">
                Find and assign Google Place IDs to leads that currently lack
                them
              </p>
            </div>
          </div>
        </div>

        {/* Error Alert */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Statistics Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Database className="h-5 w-5" />
              <span>Current Status</span>
            </CardTitle>
            <CardDescription>
              Overview of leads requiring Google Place ID assignment
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-muted/30 rounded-lg">
                <div className="text-2xl font-bold text-orange-600">
                  {leadsWithoutPlaceId.length}
                </div>
                <div className="text-sm text-muted-foreground">
                  Leads without Place ID
                </div>
              </div>
              <div className="text-center p-4 bg-muted/30 rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {
                    searchResults.filter(
                      (r) => r.placeId && r.placeId !== "None"
                    ).length
                  }
                </div>
                <div className="text-sm text-muted-foreground">
                  Place IDs found
                </div>
              </div>
              <div className="text-center p-4 bg-muted/30 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">
                  {selectedResults.size}
                </div>
                <div className="text-sm text-muted-foreground">
                  Selected for update
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Search Action Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Search className="h-5 w-5" />
              <span>Bulk Search</span>
            </CardTitle>
            <CardDescription>
              Search for Google Place IDs using business names and addresses
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">
                    This will search for Google Place IDs for all{" "}
                    {leadsWithoutPlaceId.length} leads using the cost-optimized
                    Google Places Text Search API (ID Only SKU) with location
                    bias when available.
                  </p>
                  <p className="text-xs text-muted-foreground mt-1">
                    Trusts Google's search algorithm - all found Place IDs will
                    be auto-selected for update.
                  </p>
                </div>
                <div className="flex gap-2">
                  <Button
                    onClick={startBulkSearch}
                    disabled={searching || leadsWithoutPlaceId.length === 0}
                    size="lg"
                  >
                    {searching ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Searching...
                      </>
                    ) : (
                      <>
                        <Search className="h-4 w-4 mr-2" />
                        Start Search
                      </>
                    )}
                  </Button>

                  {searching && (
                    <Button
                      onClick={stopBulkSearch}
                      variant="outline"
                      size="lg"
                    >
                      <X className="h-4 w-4 mr-2" />
                      Stop
                    </Button>
                  )}
                </div>
              </div>

              {/* Progress Display */}
              {(searching || progress.inProgress) && (
                <div className="space-y-3 p-4 bg-muted/30 rounded-lg">
                  <div className="flex justify-between text-sm">
                    <span>Search Progress</span>
                    <span>
                      {progress.completed} of {progress.total}
                    </span>
                  </div>
                  <Progress
                    value={
                      progress.total > 0
                        ? (progress.completed / progress.total) * 100
                        : 0
                    }
                    className="h-2"
                  />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>
                      <span className="text-green-600">
                        {progress.successful} successful
                      </span>
                      {" • "}
                      <span className="text-red-600">
                        {progress.failed} failed
                      </span>
                    </span>
                    {progress.inProgress && (
                      <span className="flex items-center">
                        <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                        In progress...
                      </span>
                    )}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Search Results */}
        {searchResults.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-5 w-5" />
                  <span>Search Results</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const allFoundResults = searchResults
                        .filter((r) => r.placeId && r.placeId !== "None")
                        .map((r) => r.leadId);
                      setSelectedResults(new Set(allFoundResults));
                    }}
                  >
                    Select All Found
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const allResults = searchResults
                        .filter((r) => r.placeId)
                        .map((r) => r.leadId);
                      setSelectedResults(new Set(allResults));
                    }}
                  >
                    Select All (inc. None)
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedResults(new Set())}
                  >
                    Clear Selection
                  </Button>
                  <Button
                    onClick={updateSelectedPlaceIds}
                    disabled={updating || selectedResults.size === 0}
                  >
                    {updating ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Updating...
                      </>
                    ) : (
                      <>
                        <Database className="h-4 w-4 mr-2" />
                        Update {selectedResults.size} Place IDs
                      </>
                    )}
                  </Button>
                </div>
              </CardTitle>
              <CardDescription>
                Review and select Place IDs to update in the database. Leads
                with "None" will be marked as searched but no Place ID found.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {searchResults.map((result) => (
                  <div
                    key={result.leadId}
                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                      selectedResults.has(result.leadId)
                        ? "bg-blue-50 border-blue-200"
                        : "hover:bg-muted/30"
                    }`}
                    onClick={() => {
                      // Allow selection of any result (including "None" for marking as searched)
                      if (result.placeId) {
                        setSelectedResults((prev) => {
                          const newSet = new Set(prev);
                          if (newSet.has(result.leadId)) {
                            newSet.delete(result.leadId);
                          } else {
                            newSet.add(result.leadId);
                          }
                          return newSet;
                        });
                      }
                    }}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <h4 className="font-medium">{result.leadName}</h4>
                          <Badge
                            variant={
                              result.confidence === "high"
                                ? "default"
                                : result.confidence === "medium"
                                ? "secondary"
                                : "destructive"
                            }
                          >
                            {result.confidence}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          Query: {result.searchQuery}
                        </p>
                        {result.placeId && result.placeId !== "None" && (
                          <div className="space-y-1">
                            <p className="text-sm text-green-600">
                              Place ID: {result.placeId}
                            </p>
                            <a
                              href={`https://www.google.com/maps/search/?api=1&query=Google&query_place_id=${result.placeId}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800 hover:underline"
                              onClick={(e) => e.stopPropagation()} // Prevent row selection when clicking link
                            >
                              <MapPin className="h-3 w-3 mr-1" />
                              View on Google Maps
                              <ExternalLink className="h-3 w-3 ml-1" />
                            </a>
                          </div>
                        )}
                        {result.placeId === "None" && (
                          <p className="text-sm text-orange-600">
                            No Place ID found - will be marked as searched
                          </p>
                        )}
                        {result.reason && (
                          <p className="text-sm text-red-600">
                            {result.reason}
                          </p>
                        )}
                      </div>
                      <div className="flex items-center space-x-2">
                        {result.placeId && result.placeId !== "None" ? (
                          <CheckCircle className="h-5 w-5 text-green-600" />
                        ) : result.placeId === "None" ? (
                          <XCircle className="h-5 w-5 text-orange-600" />
                        ) : (
                          <XCircle className="h-5 w-5 text-red-600" />
                        )}
                        {result.placeId &&
                          selectedResults.has(result.leadId) && (
                            <div className="w-4 h-4 bg-blue-600 rounded-full flex items-center justify-center">
                              <CheckCircle className="h-3 w-3 text-white" />
                            </div>
                          )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Empty State */}
        {leadsWithoutPlaceId.length === 0 && !loading && (
          <Card>
            <CardContent className="text-center py-12">
              <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                All leads have Place IDs!
              </h3>
              <p className="text-muted-foreground">
                All leads in your database already have Google Place IDs
                assigned.
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
