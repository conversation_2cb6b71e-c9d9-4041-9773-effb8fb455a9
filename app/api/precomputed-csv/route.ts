import { NextResponse } from "next/server";
import fs from "fs";
import path from "path";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";

// API route to list all CSV files in the PrecomputedCSV folder
export async function GET() {
  try {
    // Check authentication (optional, depending on your security requirements)
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Get the absolute path to the PrecomputedCSV folder in the public directory
    const csvFolderPath = path.join(process.cwd(), "public", "PrecomputedCSV");

    // Check if the folder exists
    if (!fs.existsSync(csvFolderPath)) {
      return NextResponse.json({ files: [] });
    }

    // Read all files in the directory
    const files = fs.readdirSync(csvFolderPath);

    // Filter for CSV files only
    const csvFiles = files.filter((file) => file.endsWith(".csv"));

    // Create file objects with name and path
    const fileObjects = csvFiles.map((file) => ({
      name: file,
      // Remove the .csv extension for display name
      displayName: file.replace(".csv", ""),
      path: `/PrecomputedCSV/${file}`,
    }));

    return NextResponse.json({ files: fileObjects });
  } catch (error) {
    console.error("Error listing CSV files:", error);
    return NextResponse.json(
      { error: "Failed to list CSV files" },
      { status: 500 }
    );
  }
}
