import { NextResponse } from "next/server";
import { query } from "@/lib/db";

export async function GET() {
  try {
    // Query leads that don't have a Google Place ID (excluding those marked as "None")
    const leads = await query(`
      SELECT
        id,
        name,
        formatted_address,
        street_name,
        street_number,
        city,
        postal_code,
        canton,
        country,
        international_phone,
        national_phone,
        ST_X(location::geometry) as longitude,
        ST_Y(location::geometry) as latitude
      FROM "bianchi_leads"
      WHERE google_place_id IS NULL OR google_place_id = ''
      ORDER BY created_at DESC
    `);

    // Transform the results to match the expected interface
    const transformedLeads = leads.map((lead) => ({
      id: lead.id,
      name: lead.name,
      formatted_address: lead.formatted_address,
      street_name: lead.street_name,
      street_number: lead.street_number,
      city: lead.city,
      postal_code: lead.postal_code,
      canton: lead.canton,
      country: lead.country,
      international_phone: lead.international_phone,
      national_phone: lead.national_phone,
      location:
        lead.latitude && lead.longitude
          ? {
              latitude: parseFloat(lead.latitude),
              longitude: parseFloat(lead.longitude),
            }
          : null,
    }));

    return NextResponse.json({
      success: true,
      leads: transformedLeads,
      count: transformedLeads.length,
    });
  } catch (error) {
    console.error("Error fetching leads without Place IDs:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch leads without Place IDs",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
