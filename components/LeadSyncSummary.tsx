import React from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface LeadSyncSummaryProps {
  addedCount: number;
  existingCount: number;
  failedCount: number;
  totalProcessed: number;
  isLoading: boolean;
}

const LeadSyncSummary: React.FC<LeadSyncSummaryProps> = ({
  addedCount,
  existingCount,
  failedCount,
  totalProcessed,
  isLoading,
}) => {
  if (isLoading) {
    return (
      <Card className="w-full">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Lead Sync Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center p-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-700"></div>
            <span className="ml-2 text-sm text-gray-600">Syncing leads...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (totalProcessed === 0) {
    return null; // Don't show anything if no leads have been processed
  }

  return (
    <Card className="w-full">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">Lead Sync Summary</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">Total Processed:</span>
            <Badge variant="outline" className="font-mono">
              {totalProcessed}
            </Badge>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">New Leads Added:</span>
            <Badge
              variant={addedCount > 0 ? "default" : "outline"}
              className="font-mono bg-green-600"
            >
              {addedCount}
            </Badge>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">Already Existing:</span>
            <Badge variant="secondary" className="font-mono">
              {existingCount}
            </Badge>
          </div>

          {failedCount > 0 && (
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Failed:</span>
              <Badge variant="destructive" className="font-mono">
                {failedCount}
              </Badge>
            </div>
          )}

          {addedCount > 0 && (
            <div className="mt-4 text-xs text-green-600">
              ✓ Successfully added {addedCount} new leads to the database
            </div>
          )}

          {existingCount > 0 && (
            <div className="mt-1 text-xs text-gray-600">
              ℹ️ {existingCount} leads already existed in the database
            </div>
          )}

          {failedCount > 0 && (
            <div className="mt-1 text-xs text-red-600">
              ⚠️ {failedCount} leads failed to be added
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default LeadSyncSummary;
