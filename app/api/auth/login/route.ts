import { NextResponse } from "next/server";
import { authenticateUser, authenticate } from "@/lib/auth";
import { createUsersTable } from "@/lib/schema";

// Make sure the users table exists
createUsersTable().catch(console.error);

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { username, password } = body;

    // Handle new user authentication
    if (username) {
      const userSession = await authenticateUser(username, password);
      
      if (userSession) {
        return NextResponse.json({ 
          success: true,
          user: {
            username: userSession.username,
            role: userSession.role
          }
        });
      }
      
      return NextResponse.json(
        { error: "Invalid username or password" },
        { status: 401 }
      );
    } 
    // Legacy password-only authentication (for backward compatibility)
    else if (password) {
      const isAuthenticated = await authenticate(password);

      if (isAuthenticated) {
        return NextResponse.json({ success: true });
      }
      
      return NextResponse.json(
        { error: "Invalid password" },
        { status: 401 }
      );
    }
    
    return NextResponse.json(
      { error: "Username and password are required" },
      { status: 400 }
    );
  } catch (error) {
    console.error("Login error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
} 