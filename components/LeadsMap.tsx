"use client";

import { useEffect, useRef, useState } from "react";
import { Lead } from "@/types/lead";
import { Loader } from "lucide-react";

interface LeadsMapProps {
  leads: Lead[];
}

const LeadsMap = ({ leads }: LeadsMapProps) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const googleMapRef = useRef<google.maps.Map | null>(null);
  const markersRef = useRef<google.maps.Marker[]>([]);
  const [mapLoaded, setMapLoaded] = useState(false);

  // Load Google Maps script
  useEffect(() => {
    const scriptId = "google-maps-script";
    const existingScript = document.getElementById(scriptId);

    // Define the callback function if it doesn't exist
    if (!(window as any).initMap) {
      (window as any).initMap = () => {
        console.log("Google Maps API loaded by LeadsMap");
        setMapLoaded(true);
      };
    }

    if (!existingScript) {
      const script = document.createElement("script");
      script.id = scriptId;
      script.src = `https://maps.googleapis.com/maps/api/js?key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}&callback=initMap`;
      script.async = true;
      script.defer = true;
      script.onerror = () => {
        console.error("Failed to load Google Maps API script");
      };
      document.head.appendChild(script);
      console.log("Appending Google Maps script...");
    } else if (window.google && window.google.maps) {
      console.log(
        "Google Maps script already exists. Triggering map load state."
      );
      setMapLoaded(true);
    }

    return () => {
      // Clean up global callback when component unmounts
      if (typeof window !== "undefined" && (window as any).initMap) {
        (window as any).initMap = undefined;
      }
    };
  }, []);

  // Initialize map when script is loaded
  useEffect(() => {
    if (!mapLoaded || !mapRef.current || !window.google || !window.google.maps)
      return;

    console.log("Initializing Leads map...");
    try {
      const defaultCenter = { lat: 46.8182, lng: 8.2275 }; // Switzerland center
      const map = new google.maps.Map(mapRef.current, {
        center: defaultCenter,
        zoom: 8,
        mapTypeId: google.maps.MapTypeId.ROADMAP,
        mapTypeControl: true,
        streetViewControl: false,
        fullscreenControl: true,
      });
      googleMapRef.current = map;
      console.log("Leads map initialized.");
    } catch (error) {
      console.error("Error initializing Google Map:", error);
    }
  }, [mapLoaded]);

  // Display leads on the map
  useEffect(() => {
    if (!googleMapRef.current || !mapLoaded) return;

    // Clear existing markers
    markersRef.current.forEach((marker) => marker.setMap(null));
    markersRef.current = [];

    // Debug: Log the first few leads to see their structure
    console.log("First few leads:", leads.slice(0, 3));

    // Debug: Check location data format
    const locationSamples = leads.slice(0, 5).map((lead) => lead.location);
    console.log("Location data samples:", locationSamples);

    // Filter leads with valid location data
    const leadsWithLocation = leads.filter(
      (lead) =>
        lead.location && lead.location.latitude && lead.location.longitude
    );

    console.log(
      `Displaying ${leadsWithLocation.length} leads with location data out of ${leads.length} total leads`
    );

    // Debug: Log the first few leads with location data
    if (leadsWithLocation.length > 0) {
      console.log(
        "First few leads with location:",
        leadsWithLocation.slice(0, 3)
      );
    } else {
      console.log("No leads with valid location data found");
    }

    if (leadsWithLocation.length === 0) return;

    // Create bounds to fit all markers
    const bounds = new google.maps.LatLngBounds();

    // Create markers for each lead
    const markers = leadsWithLocation.map((lead) => {
      const position = {
        lat: lead.location!.latitude,
        lng: lead.location!.longitude,
      };

      // Extend bounds to include this position
      bounds.extend(position);

      // Create marker
      const marker = new google.maps.Marker({
        position,
        map: googleMapRef.current || undefined,
        title: lead.name,
      });

      // Create info window with lead details
      const infoWindow = new google.maps.InfoWindow({
        content: `
          <div style="padding: 8px; max-width: 250px;">
            <h3 style="margin: 0 0 8px; font-size: 16px;">${lead.name}</h3>
            <p style="margin: 0 0 4px;"><strong>Address:</strong> ${
              lead.formatted_address || "N/A"
            }</p>
            ${
              lead.business_status
                ? `<p style="margin: 0 0 4px;"><strong>Status:</strong> ${lead.business_status}</p>`
                : ""
            }
            ${
              lead.international_phone
                ? `<p style="margin: 0 0 4px;"><strong>Phone:</strong> ${lead.international_phone}</p>`
                : ""
            }
            ${
              lead.website_uri
                ? `<p style="margin: 0 0 4px;"><strong>Website:</strong> <a href="${lead.website_uri}" target="_blank">${lead.website_uri}</a></p>`
                : ""
            }
            ${
              lead.rating
                ? `<p style="margin: 0 0 4px;"><strong>Rating:</strong> ${
                    lead.rating
                  } (${lead.user_rating_count || 0} reviews)</p>`
                : ""
            }
          </div>
        `,
      });

      // Add click event to open info window
      google.maps.event.addListener(marker, "click", () => {
        if (googleMapRef.current) {
          infoWindow.open({
            map: googleMapRef.current,
            anchor: marker,
          });
        }
      });

      return marker;
    });

    // Store markers for later cleanup
    markersRef.current = markers;

    // Fit map to bounds if map is available
    if (googleMapRef.current) {
      googleMapRef.current.fitBounds(bounds);

      // If there's only one marker, zoom out a bit
      if (leadsWithLocation.length === 1) {
        googleMapRef.current.setZoom(14);
      }
    }
  }, [leads, mapLoaded]);

  return (
    <div className="mt-8 border rounded-md overflow-hidden">
      <div className="bg-muted p-3 border-b">
        <h2 className="text-lg font-medium">Leads Map</h2>
        <p className="text-sm text-muted-foreground">
          Displaying{" "}
          {
            leads.filter(
              (lead) =>
                lead.location &&
                lead.location.latitude &&
                lead.location.longitude
            ).length
          }{" "}
          leads with location data
        </p>
      </div>
      <div className="h-[500px] w-full relative">
        {!mapLoaded && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-80 z-10">
            <Loader className="w-8 h-8 animate-spin text-blue-500" />
            <span className="ml-2">Loading map...</span>
          </div>
        )}
        <div ref={mapRef} className="h-full w-full" />
      </div>
    </div>
  );
};

export default LeadsMap;
