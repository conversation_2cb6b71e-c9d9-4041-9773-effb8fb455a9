import fs from 'fs';
import path from 'path';
import { Pool } from 'pg';
import dotenv from 'dotenv';
import { createAuthTables } from './create_auth_tables.js';
import { runMigration } from './create_auth_tables';

// Load environment variables from .env.local
dotenv.config({ path: '.env.local' });

// Create a database connection
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: {
    rejectUnauthorized: false, // Required for Neon
  },
});

// Order matters for dependencies
const migrationFiles = [
  'create_leads_table.sql',  // This should run first since it has fewer dependencies
  'create_logs_table.sql',   // Order is important for potential foreign key relationships
  // 'create_users_table.sql', // No longer needed - Auth.js provides its own users table
  // Auth.js tables will be handled separately
];

async function runMigrations() {
  const client = await pool.connect();
  
  try {
    console.log('Starting database migrations...');
    
    // Execute each migration in order
    for (const file of migrationFiles) {
      console.log(`Running migration: ${file}`);
      
      const sqlFilePath = path.join(__dirname, file);
      
      if (!fs.existsSync(sqlFilePath)) {
        console.error(`Migration file not found: ${sqlFilePath}`);
        continue;
      }
      
      const sql = fs.readFileSync(sqlFilePath, 'utf8');
      await client.query(sql);
      
      console.log(`Completed migration: ${file}`);
    }
    
    // Run Auth.js tables migrations
    console.log('🚀 Starting Auth.js migration...');
    await runMigration();
    
    console.log('✅ Auth.js migration completed successfully!');
    
    console.log('All migrations completed successfully.');
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  } finally {
    client.release();
    await pool.end();
  }
}

// Run all migrations
runMigrations(); 