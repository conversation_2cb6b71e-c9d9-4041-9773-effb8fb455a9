"use client";

import React, { useState, useRef, useEffect, useCallback } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader } from "lucide-react";
import { toast } from "sonner";
import { Feature, Polygon as GeoJsonPolygon } from "geojson"; // Import GeoJSON types

interface MapBounds {
  north: number;
  south: number;
  east: number;
  west: number;
}

interface CirclePoint {
  lat: number;
  lng: number;
  radius: number;
  density?: number;
}

interface SearchResultPoint {
  lat: number;
  lng: number;
  place_id?: string; // Optional place ID
  name?: string; // Optional place name
}

interface ExtensiveLeadGeneratorProps {
  onStartSearch: (points: CirclePoint[]) => Promise<SearchResultPoint[]>;
  loading: boolean;
}

const ExtensiveLeadGenerator: React.FC<ExtensiveLeadGeneratorProps> = ({
  onStartSearch,
  loading,
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const googleMapRef = useRef<google.maps.Map | null>(null);
  const drawingManagerRef = useRef<google.maps.drawing.DrawingManager | null>(
    null
  );
  const searchRadiusInputRef = useRef<HTMLInputElement>(null);
  const [selectedArea, setSelectedArea] = useState<google.maps.Polygon | null>(
    null
  );
  const [searchRadius, setSearchRadius] = useState(200); // Default 200m radius
  const [searchPoints, setSearchPoints] = useState<CirclePoint[]>([]);
  const [mapLoaded, setMapLoaded] = useState(false);
  const [generatingPoints, setGeneratingPoints] = useState(false);
  const [useAdaptiveRadius, setUseAdaptiveRadius] = useState(true);
  const [minRadius, setMinRadius] = useState(200);
  const [maxRadius, setMaxRadius] = useState(1000);

  // Visualize points function (moved and wrapped in useCallback)
  const visualizePoints = useCallback(
    (points: CirclePoint[]) => {
      if (!googleMapRef.current) return;

      // Save current map viewport
      const currentCenter = googleMapRef.current.getCenter();
      const currentZoom = googleMapRef.current.getZoom();

      // Clear any existing circles
      if (googleMapRef.current.get("circles")) {
        (googleMapRef.current.get("circles") as google.maps.Circle[]).forEach(
          (circle) => {
            circle.setMap(null);
          }
        );
      }

      // Create new array to store circles
      const circles: google.maps.Circle[] = [];

      // Define color gradient based on density (not radius) for adaptive radius
      const getCircleColor = (
        point: CirclePoint
      ): { fill: string; stroke: string } => {
        // Only use this for adaptive radius
        if (!useAdaptiveRadius) {
          return { fill: "#4ade80", stroke: "#16a34a" }; // Default green for fixed radius
        }

        // No density data available
        if (point.density === undefined) {
          return { fill: "#a1a1aa", stroke: "#71717a" }; // Gray for unknown density
        }

        // For adaptive radius, use color gradient based on density value
        if (point.density > 1500) {
          return { fill: "#ef4444", stroke: "#b91c1c" }; // Red for high density (>1500)
        } else if (point.density > 750) {
          return { fill: "#f97316", stroke: "#c2410c" }; // Orange for medium-high density (750-1500)
        } else if (point.density > 300) {
          return { fill: "#facc15", stroke: "#ca8a04" }; // Yellow for medium density (300-750)
        } else if (point.density > 100) {
          return { fill: "#22c55e", stroke: "#15803d" }; // Green for medium-low density (100-300)
        } else {
          return { fill: "#3b82f6", stroke: "#1d4ed8" }; // Blue for low density (<100)
        }
      };

      // Add circles for each point
      points.forEach((point) => {
        // Ensure map is not null before creating circle
        if (!googleMapRef.current) return;

        const circleColor = getCircleColor(point);

        const circle = new google.maps.Circle({
          map: googleMapRef.current,
          center: { lat: point.lat, lng: point.lng },
          radius: point.radius,
          fillColor: circleColor.fill,
          fillOpacity: 0.3,
          strokeColor: circleColor.stroke,
          strokeWeight: 1,
        });
        circles.push(circle);
      });

      // Store circles array on map instance for later cleanup
      googleMapRef.current.set("circles", circles);

      // Optionally restore map viewport if circles were added
      if (circles.length > 0 && currentCenter && currentZoom) {
        // googleMapRef.current.setCenter(currentCenter);
        // googleMapRef.current.setZoom(currentZoom);
        // Consider if viewport restoration is desired or if fitting bounds is better
        // Example: Fit bounds to the new circles
        // const bounds = new google.maps.LatLngBounds();
        // circles.forEach(circle => bounds.union(circle.getBounds()!));
        // googleMapRef.current.fitBounds(bounds);
      }
    },
    [googleMapRef, useAdaptiveRadius]
  ); // Add dependencies

  // Function to visualize search results on the map
  const visualizeSearchResults = useCallback(
    (results: SearchResultPoint[]) => {
      if (!googleMapRef.current) return;

      // Log the results for debugging
      console.log(`Visualizing ${results.length} search results on the map`);

      // Create markers for each result
      const markers: google.maps.Marker[] = [];

      results.forEach((result) => {
        if (!googleMapRef.current) return;

        // Create a marker for the result
        const marker = new google.maps.Marker({
          position: { lat: result.lat, lng: result.lng },
          map: googleMapRef.current,
          title:
            result.name || `${result.lat.toFixed(6)}, ${result.lng.toFixed(6)}`,
          icon: {
            path: google.maps.SymbolPath.CIRCLE,
            fillColor: "#FF0000", // Red color
            fillOpacity: 1,
            strokeWeight: 1,
            strokeColor: "#FFFFFF",
            scale: 8, // Size of the marker
          },
        });

        // Add an info window for the marker
        const infoWindow = new google.maps.InfoWindow({
          content: `<div style="padding: 8px; max-width: 200px;">
          <strong>${result.name || "Location"}</strong><br>
          <strong>Coordinates:</strong> ${result.lat.toFixed(
            6
          )}, ${result.lng.toFixed(6)}<br>
          ${
            result.place_id
              ? `<strong>Place ID:</strong> ${result.place_id}`
              : ""
          }
        </div>`,
        });

        // Add a click listener to show the info window
        google.maps.event.addListener(marker, "click", () => {
          // Use the new InfoWindowOpenOptions interface
          if (googleMapRef.current) {
            infoWindow.open({
              map: googleMapRef.current,
              anchor: marker,
            });
          }
        });

        markers.push(marker);
      });

      // Store the markers on the map for later cleanup
      googleMapRef.current.set("searchMarkers", markers);

      toast.success(`Displaying ${markers.length} search results on the map`);
    },
    [googleMapRef]
  );

  // Backend coverage generation function
  const generateCoverageOnBackend = useCallback(
    async (polygon: Feature<GeoJsonPolygon>, minR: number, maxR: number) => {
      setGeneratingPoints(true);
      toast.info("Generating H3-based coverage points via backend...");

      const payload = {
        polygon: polygon,
        minRadius: minR,
        maxRadius: maxR,
      };

      try {
        const response = await fetch("/api/earth-engine/population-density", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(payload),
        });

        const data = await response.json();

        if (!response.ok) {
          const errorMsg = data.error || "Unknown error from backend";
          console.error("Error generating coverage:", errorMsg);
          throw new Error(`Failed to generate coverage: ${errorMsg}`);
        }

        if (data.points && Array.isArray(data.points)) {
          setSearchPoints(data.points);
          visualizePoints(data.points);
          toast.success(`Generated ${data.points.length} coverage points`);
        } else {
          throw new Error("Invalid response format from coverage API");
        }
      } catch (error) {
        console.error("Error in generateCoverageOnBackend:", error);
        const errorMsg =
          error instanceof Error
            ? error.message
            : "An unexpected error occurred";
        toast.error(`Coverage Generation Failed: ${errorMsg}`);
        setSearchPoints([]); // Clear points on error
        visualizePoints([]); // Clear visualization
      } finally {
        setGeneratingPoints(false);
      }
    },
    [setGeneratingPoints, setSearchPoints, visualizePoints]
  ); // visualizePoints is now stable

  // Load Google Maps script
  useEffect(() => {
    // Guard against SSR
    if (typeof window === "undefined") return;

    const loadGoogleMapsScript = () => {
      const existingScript = document.getElementById("google-maps-script");
      if (!existingScript) {
        const script = document.createElement("script");
        script.id = "google-maps-script";
        script.src = `https://maps.googleapis.com/maps/api/js?key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}&libraries=drawing,geometry&callback=initMap`;
        script.async = true;
        script.defer = true;

        // Define the global callback
        window.initMap = () => {
          setMapLoaded(true);
        };

        // Add error handling for script loading
        script.onerror = () => {
          console.error("Failed to load Google Maps API script");
          toast.error(
            "Failed to load Google Maps. Please check your internet connection and try again."
          );
        };

        document.head.appendChild(script);
      } else if (window.google && window.google.maps) {
        // Google Maps API already loaded
        setMapLoaded(true);
      }
    };

    loadGoogleMapsScript();

    // Expose the visualizeSearchResults function to the window object
    window.visualizeSearchResultsOnMap = visualizeSearchResults;
    console.log("Exposed visualizeSearchResultsOnMap to window object");

    return () => {
      // Clean up global callback
      if (typeof window !== "undefined") {
        if ("initMap" in window) {
          // Use type assertion to fix the TypeScript error
          (window as any).initMap = undefined;
        }
        if ("visualizeSearchResultsOnMap" in window) {
          (window as any).visualizeSearchResultsOnMap = undefined;
        }
      }
    };
  }, [visualizeSearchResults]);

  // Effect to initialize map and drawing tools
  useEffect(() => {
    // Guard against SSR
    if (typeof window === "undefined") return;
    if (!mapLoaded || !mapRef.current || !window.google || !window.google.maps)
      return;

    try {
      // Initialize map centered on Switzerland
      const switzerlandCenter = { lat: 46.8182, lng: 8.2275 };
      const map = new google.maps.Map(mapRef.current, {
        center: switzerlandCenter,
        zoom: 8,
        mapTypeId: google.maps.MapTypeId.ROADMAP,
        mapTypeControl: true,
        streetViewControl: false,
        fullscreenControl: false,
      });

      googleMapRef.current = map;

      // Initialize drawing manager
      const drawingManager = new google.maps.drawing.DrawingManager({
        drawingMode: google.maps.drawing.OverlayType.POLYGON,
        drawingControl: true,
        drawingControlOptions: {
          position: google.maps.ControlPosition.TOP_CENTER,
          drawingModes: [google.maps.drawing.OverlayType.POLYGON],
        },
        polygonOptions: {
          editable: true,
          fillColor: "#3b82f6",
          fillOpacity: 0.3,
          strokeColor: "#1d4ed8",
          strokeWeight: 2,
        },
      });

      drawingManager.setMap(map);
      drawingManagerRef.current = drawingManager;

      // Add event listener for polygon complete
      google.maps.event.addListener(
        drawingManager,
        "polygoncomplete",
        function polygonCompleteHandler(polygon: google.maps.Polygon) {
          // Save current map viewport BEFORE any operations
          const currentCenter = map.getCenter();
          const currentZoom = map.getZoom();

          // Only allow one polygon at a time
          if (selectedArea) {
            selectedArea.setMap(null);
          }

          // Make sure the polygon stays on the map
          polygon.setMap(map);
          setSelectedArea(polygon);
          drawingManager.setDrawingMode(null);

          // Convert Google Polygon to GeoJSON Feature
          const geoJsonPolygon = googlePolygonToGeoJsonFeature(polygon);

          // Get current radius settings
          const currentMinRadius = minRadius;
          const currentMaxRadius = maxRadius;

          // Call the backend generation function with updated parameters
          generateCoverageOnBackend(
            geoJsonPolygon,
            currentMinRadius,
            currentMaxRadius
          );
        }
      );
    } catch (error) {
      console.error("Error initializing Google Maps:", error);
      toast.error("Failed to initialize the map. Please reload the page.");
    }

    return () => {
      if (drawingManagerRef.current) {
        // Clean up drawing manager listeners if any were added directly to it
        // google.maps.event.clearInstanceListeners(drawingManagerRef.current); // Removed problematic call
        drawingManagerRef.current.setMap(null);
      }
      // Also clean up polygon listeners if the component unmounts while a polygon exists
      if (selectedArea) {
        // const path = selectedArea.getPath();
        // google.maps.event.clearInstanceListeners(path); // Removed problematic call
        // google.maps.event.clearInstanceListeners(selectedArea); // Removed problematic call
        selectedArea.setMap(null); // Setting map to null handles cleanup
      }
      // Clean up map instance listeners if necessary
      if (googleMapRef.current) {
        // google.maps.event.clearInstanceListeners(googleMapRef.current); // Removed problematic call
        // Map instance itself doesn't usually need explicit listener cleanup unless specific listeners were added directly to it
      }
    };
  }, [
    mapLoaded,
    selectedArea,
    minRadius,
    maxRadius,
    generateCoverageOnBackend,
  ]); // Now generateCoverageOnBackend can be included safely

  // Helper function to convert Google Maps Polygon to GeoJSON Feature<Polygon>
  const googlePolygonToGeoJsonFeature = (
    polygon: google.maps.Polygon
  ): Feature<GeoJsonPolygon> => {
    const path = polygon.getPath(); // Use getPath() for single polygon
    const coordinates: number[][][] = [];
    const pathCoords: number[][] = [];

    // Iterate over the single path
    path.forEach((latLng: google.maps.LatLng) => {
      // Add type for latLng
      pathCoords.push([latLng.lng(), latLng.lat()]);
    });

    // Close the ring if necessary (outer ring)
    if (
      pathCoords.length > 0 &&
      (pathCoords[0][0] !== pathCoords[pathCoords.length - 1][0] ||
        pathCoords[0][1] !== pathCoords[pathCoords.length - 1][1])
    ) {
      pathCoords.push([...pathCoords[0]]);
    }

    // GeoJSON Polygon coordinates are an array of rings.
    // For a simple polygon, it's one ring.
    coordinates.push(pathCoords);

    return {
      type: "Feature",
      properties: {},
      geometry: {
        type: "Polygon",
        coordinates: coordinates,
      },
    };
  };

  // Function to start the search process
  const handleStartSearch = async () => {
    if (searchPoints.length === 0) {
      toast.error("Please generate search points first");
      return;
    }

    console.log(`Starting extensive search with ${searchPoints.length} points`);

    try {
      // Call the search function and get the results
      const searchResults = await onStartSearch(searchPoints);

      // Log the search results
      console.log(
        `Received ${searchResults.length} search results:`,
        searchResults
      );

      // Visualize the search results on the map
      if (searchResults && searchResults.length > 0) {
        visualizeSearchResults(searchResults);
      } else {
        toast.info("No results found with valid coordinates.");
      }
    } catch (error) {
      console.error("Error handling search results:", error);
      toast.error("Failed to process search results.");
    }
  };

  // Function to clear the map
  const handleClearMap = () => {
    if (selectedArea) {
      selectedArea.setMap(null);
      setSelectedArea(null);
    }

    // Clear circles from map
    if (googleMapRef.current) {
      const existingCircles = googleMapRef.current.get("circles") as
        | google.maps.Circle[]
        | undefined;
      if (existingCircles) {
        existingCircles.forEach((circle) => {
          // Clean up listeners on each circle before removing
          // google.maps.event.clearInstanceListeners(circle); // Removed problematic call
          circle.setMap(null); // Setting map to null handles cleanup
        });
        googleMapRef.current.set("circles", []); // Clear the stored array
      }

      // Clear search result markers
      const searchMarkers = googleMapRef.current.get("searchMarkers") as
        | google.maps.Marker[]
        | undefined;
      if (searchMarkers) {
        searchMarkers.forEach((marker) => {
          marker.setMap(null);
        });
        googleMapRef.current.set("searchMarkers", []);
        console.log("Cleared search result markers");
      }
    }

    setSearchPoints([]);
  };

  return (
    <Card className="bg-purple-50/50">
      <CardContent className="p-4 grid gap-4">
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="adaptive-radius"
              checked={useAdaptiveRadius}
              onChange={(e) => setUseAdaptiveRadius(e.target.checked)}
              className="rounded"
            />
            <Label htmlFor="adaptive-radius">
              Use population density-based adaptive radius
            </Label>
          </div>

          {useAdaptiveRadius ? (
            <div className="grid grid-cols-2 gap-4 p-4 bg-blue-50 rounded-md">
              <div className="space-y-2">
                <Label htmlFor="min-radius">Minimum Radius (dense areas)</Label>
                <Input
                  id="min-radius"
                  type="number"
                  value={minRadius}
                  onChange={(e) => setMinRadius(Number(e.target.value))}
                  min={50}
                  max={1000}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="max-radius">
                  Maximum Radius (sparse areas)
                </Label>
                <Input
                  id="max-radius"
                  type="number"
                  value={maxRadius}
                  onChange={(e) => setMaxRadius(Number(e.target.value))}
                  min={200}
                  max={5000}
                />
              </div>
              <div className="col-span-2">
                <div className="flex flex-col gap-1">
                  <div className="flex justify-between">
                    <span className="text-xs font-medium text-red-500">
                      High density: {minRadius}m
                    </span>
                    <span className="text-xs font-medium text-blue-500">
                      Low density: {maxRadius}m
                    </span>
                  </div>
                  <div className="h-2 bg-gradient-to-r from-red-500 via-yellow-500 to-blue-500 rounded-full" />
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              <Label htmlFor="search-radius">
                Fixed Search Radius (meters)
              </Label>
              <Input
                id="search-radius"
                type="number"
                value={searchRadius}
                onChange={(e) => setSearchRadius(Number(e.target.value))}
                min={50}
                max={10000}
                ref={searchRadiusInputRef}
              />
              <p className="text-xs text-muted-foreground">
                Google Places API works best with 200-5000m radius
              </p>
            </div>
          )}
        </div>

        <div className="h-[600px] w-full relative">
          {!mapLoaded && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-80 z-10">
              <Loader className="w-8 h-8 animate-spin text-blue-500" />
              <span className="ml-2">Loading map...</span>
            </div>
          )}
          <div
            ref={mapRef}
            className="h-full w-full rounded-md border border-gray-200"
          />
        </div>

        <div className="space-y-2">
          <div className="text-sm text-muted-foreground">
            Draw a polygon on the map to select the search area. Points will be
            automatically generated when you complete the polygon.
          </div>

          <div className="flex space-x-2">
            <Button onClick={handleClearMap} className="flex-1">
              Clear Map
            </Button>

            <Button
              onClick={handleStartSearch}
              disabled={
                searchPoints.length === 0 || loading || generatingPoints
              }
              className="flex-1"
              variant="default"
            >
              {loading ? (
                <>
                  <Loader className="mr-2 h-4 w-4 animate-spin" />
                  Searching...
                </>
              ) : generatingPoints ? (
                <>
                  <Loader className="mr-2 h-4 w-4 animate-spin" />
                  Generating Points...
                </>
              ) : (
                "Start Search"
              )}
            </Button>
          </div>
        </div>

        {searchPoints.length > 0 && !generatingPoints && (
          <div className="bg-white p-4 rounded-md border space-y-3">
            <div className="flex justify-between items-center">
              <h3 className="font-medium">Coverage Summary</h3>
              <span className="text-sm bg-green-100 text-green-800 rounded-full px-2 py-1">
                {searchPoints.length} search points
              </span>
            </div>
            {useAdaptiveRadius && (
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">
                  Using population density data to optimize search radius:
                </p>
                <div className="grid grid-cols-1 gap-2">
                  <div className="text-xs text-muted-foreground flex items-center space-x-1">
                    <div className="w-4 h-4 rounded-full bg-red-500"></div>
                    <span>High Density (&gt;1500): {minRadius}m radius</span>
                  </div>
                  <div className="text-xs text-muted-foreground flex items-center space-x-1">
                    <div className="w-4 h-4 rounded-full bg-orange-500"></div>
                    <span>Medium-High Density (750-1500)</span>
                  </div>
                  <div className="text-xs text-muted-foreground flex items-center space-x-1">
                    <div className="w-4 h-4 rounded-full bg-yellow-500"></div>
                    <span>Medium Density (300-750)</span>
                  </div>
                  <div className="text-xs text-muted-foreground flex items-center space-x-1">
                    <div className="w-4 h-4 rounded-full bg-green-500"></div>
                    <span>Medium-Low Density (100-300)</span>
                  </div>
                  <div className="text-xs text-muted-foreground flex items-center space-x-1">
                    <div className="w-4 h-4 rounded-full bg-blue-500"></div>
                    <span>Low Density (&lt;100): {maxRadius}m radius</span>
                  </div>
                </div>
                <p className="text-xs text-muted-foreground mt-2">
                  <strong>Note:</strong> Hover over any circle to see exact
                  density and radius values.
                </p>
              </div>
            )}
            <p className="text-sm text-muted-foreground">
              Using adaptive H3 hexagonal grid pattern based on population
              density.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// Need to define the global initMap function for Google Maps
declare global {
  interface Window {
    initMap: () => void;
    visualizeSearchResultsOnMap?: (results: SearchResultPoint[]) => void;
  }
}

export default ExtensiveLeadGenerator;
