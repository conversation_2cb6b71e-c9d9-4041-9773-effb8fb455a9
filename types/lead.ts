export interface Lead {
  id: number;
  source: string;
  google_place_id: string | null;
  erp_id: string | null;
  name: string;
  formatted_address: string;
  address_components: Record<string, any> | null;
  street_name: string;
  street_number: string;
  city: string;
  postal_code: string;
  canton: string;
  country: string;
  international_phone: string | null;
  national_phone: string | null;
  website_uri: string | null;
  business_status: string;
  price_level: number | null;
  rating: number | null;
  user_rating_count: number | null;
  reviews: Record<string, any>[] | null;
  opening_hours: Record<string, any> | null;
  types: string[];
  location: {
    latitude: number;
    longitude: number;
  } | null;
  plus_code: string | null;
  created_at: string;
  updated_at: string;
  approved: boolean | null;
  language_code: string | null;
  language_description: string | null;
  name1: string | null;
  name2: string | null;
  name3: string | null;
  email: string | null;
  contact_person_email: string | null;
  address_group: string | null;
  address_group_description: string | null;
  representative1: string | null;
  representative2: string | null;
  parent_group: string | null;
  parent_group_description: string | null;
  group: string | null;
  group_description: string | null;
  business_type: string | null;
  business_type_description: string | null;
  salutation_number: string | null;
  salutation_description: string | null;
  contact_person_first_name: string | null;
  contact_person_last_name: string | null;
}
