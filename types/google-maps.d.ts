// Type definitions for Google Maps JavaScript API
declare namespace google.maps {
  class Map {
    constructor(mapDiv: Element | null, opts?: MapOptions);
    setCenter(latLng: LatLng | LatLngLiteral): void;
    setZoom(zoom: number): void;
    setMap(map: Map | null): void;
    getCenter(): LatLng;
    getZoom(): number;
    getBounds(): LatLngBounds;
    panTo(latLng: LatLng | LatLngLiteral): void;
    panBy(x: number, y: number): void;
    setOptions(options: MapOptions): void;
    overlayMapTypes: MVCArray<any>;
    get(key: string): any;
    set(key: string, value: any): void;
    fitBounds(
      bounds: LatLngBounds | LatLngBoundsLiteral,
      padding?: number | Padding
    ): void;
  }

  interface Padding {
    top: number;
    right: number;
    bottom: number;
    left: number;
  }

  interface LatLngBoundsLiteral {
    east: number;
    north: number;
    south: number;
    west: number;
  }

  interface MapOptions {
    center?: LatLng | LatLngLiteral;
    zoom?: number;
    mapTypeId?: string;
    mapTypeControl?: boolean;
    streetViewControl?: boolean;
    fullscreenControl?: boolean;
  }

  interface LatLngLiteral {
    lat: number;
    lng: number;
  }

  class LatLng {
    constructor(lat: number, lng: number, noWrap?: boolean);
    lat(): number;
    lng(): number;
    toString(): string;
    toUrlValue(precision?: number): string;
    toJSON(): LatLngLiteral;
  }

  class LatLngBounds {
    constructor(sw?: LatLng | LatLngLiteral, ne?: LatLng | LatLngLiteral);
    extend(point: LatLng | LatLngLiteral): LatLngBounds;
    getCenter(): LatLng;
    getNorthEast(): LatLng;
    getSouthWest(): LatLng;
    isEmpty(): boolean;
    contains(latLng: LatLng | LatLngLiteral): boolean;
    equals(other: LatLngBounds | null): boolean;
    toString(): string;
    toJSON(): Array<LatLngLiteral>;
    toUrlValue(precision?: number): string;
    union(other: LatLngBounds): LatLngBounds;
  }

  class MVCArray<T> {
    constructor(array?: Array<T>);
    clear(): void;
    getArray(): Array<T>;
    getAt(i: number): T;
    getLength(): number;
    insertAt(i: number, elem: T): void;
    pop(): T;
    push(elem: T): number;
    removeAt(i: number): T;
    setAt(i: number, elem: T): void;
    forEach(callback: (elem: T, i: number) => void): void;
  }

  class Circle {
    constructor(opts?: CircleOptions);
    getCenter(): LatLng;
    getRadius(): number;
    setCenter(center: LatLng | LatLngLiteral): void;
    setMap(map: Map | null): void;
    setOptions(options: CircleOptions): void;
    setRadius(radius: number): void;
  }

  interface CircleOptions {
    center?: LatLng | LatLngLiteral;
    clickable?: boolean;
    draggable?: boolean;
    editable?: boolean;
    fillColor?: string;
    fillOpacity?: number;
    map?: Map;
    radius?: number;
    strokeColor?: string;
    strokeOpacity?: number;
    strokePosition?: number;
    strokeWeight?: number;
    visible?: boolean;
    zIndex?: number;
  }

  class Marker {
    constructor(opts?: MarkerOptions);
    getPosition(): LatLng | null;
    setMap(map: Map | null): void;
    setPosition(latLng: LatLng | LatLngLiteral): void;
  }

  interface MarkerOptions {
    position: LatLng | LatLngLiteral;
    map?: Map;
    title?: string;
    draggable?: boolean;
    animation?: any;
    icon?: string | Icon | Symbol;
  }

  interface Icon {
    url?: string;
    size?: Size;
    origin?: Point;
    anchor?: Point;
    scaledSize?: Size;
    path?: string;
    fillColor?: string;
    fillOpacity?: number;
    strokeWeight?: number;
    strokeColor?: string;
    scale?: number;
  }

  interface Symbol {
    path: string | SymbolPath;
    fillColor?: string;
    fillOpacity?: number;
    strokeColor?: string;
    strokeOpacity?: number;
    strokeWeight?: number;
    scale?: number;
  }

  enum SymbolPath {
    CIRCLE = 0,
    FORWARD_CLOSED_ARROW = 1,
    FORWARD_OPEN_ARROW = 2,
    BACKWARD_CLOSED_ARROW = 3,
    BACKWARD_OPEN_ARROW = 4,
  }

  class Size {
    constructor(
      width: number,
      height: number,
      widthUnit?: string,
      heightUnit?: string
    );
    width: number;
    height: number;
    equals(other: Size): boolean;
    toString(): string;
  }

  class Point {
    constructor(x: number, y: number);
    x: number;
    y: number;
    equals(other: Point): boolean;
    toString(): string;
  }

  class InfoWindow {
    constructor(opts?: InfoWindowOptions);
    close(): void;
    getContent(): string | Element;
    getPosition(): LatLng | null;
    open(options?: InfoWindowOpenOptions): void;
    setContent(content: string | Element): void;
    setPosition(position: LatLng | LatLngLiteral): void;
  }

  interface InfoWindowOptions {
    content?: string | Element;
    position?: LatLng | LatLngLiteral;
    maxWidth?: number;
  }

  interface InfoWindowOpenOptions {
    map?: Map;
    anchor?: Marker;
  }

  class Polygon {
    constructor(opts?: PolygonOptions);
    getPath(): MVCArray<LatLng>;
    setMap(map: Map | null): void;
    setOptions(options: PolygonOptions): void;
  }

  interface PolygonOptions {
    paths?: Array<LatLng | LatLngLiteral> | MVCArray<LatLng | LatLngLiteral>;
    strokeColor?: string;
    strokeOpacity?: number;
    strokeWeight?: number;
    fillColor?: string;
    fillOpacity?: number;
    editable?: boolean;
    map?: Map;
  }

  namespace event {
    function addListener(
      instance: any,
      eventName: string,
      handler: (...args: any[]) => void
    ): any;
    function removeListener(listener: any): void;
    function clearListeners(instance: any, eventName: string): void;
  }

  namespace drawing {
    class DrawingManager {
      constructor(options?: DrawingManagerOptions);
      setMap(map: Map | null): void;
      setDrawingMode(drawingMode: string | null): void;
    }

    interface DrawingManagerOptions {
      drawingMode?: string | null;
      drawingControl?: boolean;
      drawingControlOptions?: DrawingControlOptions;
      polygonOptions?: PolygonOptions;
      map?: Map;
    }

    interface DrawingControlOptions {
      position?: any;
      drawingModes?: string[];
    }

    const OverlayType: {
      POLYGON: string;
      MARKER: string;
      POLYLINE: string;
      RECTANGLE: string;
      CIRCLE: string;
    };
  }

  namespace geometry {
    namespace poly {
      function containsLocation(point: LatLng, polygon: Polygon): boolean;
    }
  }

  const MapTypeId: {
    ROADMAP: string;
    SATELLITE: string;
    HYBRID: string;
    TERRAIN: string;
  };

  const ControlPosition: {
    TOP_CENTER: number;
    TOP_LEFT: number;
    TOP_RIGHT: number;
    LEFT_TOP: number;
    RIGHT_TOP: number;
    LEFT_CENTER: number;
    RIGHT_CENTER: number;
    LEFT_BOTTOM: number;
    RIGHT_BOTTOM: number;
    BOTTOM_CENTER: number;
    BOTTOM_LEFT: number;
    BOTTOM_RIGHT: number;
  };
}

declare global {
  interface Window {
    // Allow initMap to be undefined or a function
    initMap?: (() => void) | undefined;
    google: typeof google;
  }
}
