import React from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";

interface PlaceIdSyncSummaryProps {
  updatedCount: number;
  failedCount: number;
  totalCount: number;
  isLoading: boolean;
  progress: number; // 0-100
}

const PlaceIdSyncSummary: React.FC<PlaceIdSyncSummaryProps> = ({
  updatedCount,
  failedCount,
  totalCount,
  isLoading,
  progress,
}) => {
  if (isLoading) {
    return (
      <Card className="w-full">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Google Place ID Sync Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Processing leads...</span>
              <span className="text-sm font-medium">{progress.toFixed(0)}%</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (totalCount === 0) {
    return null; // Don't show anything if no leads have been processed
  }

  return (
    <Card className="w-full">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">Google Place ID Sync Summary</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">Total Processed:</span>
            <Badge variant="outline" className="font-mono">
              {totalCount}
            </Badge>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">IDs Found:</span>
            <Badge
              variant={updatedCount > 0 ? "default" : "outline"}
              className="font-mono bg-green-600"
            >
              {updatedCount}
            </Badge>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">Not Found:</span>
            <Badge variant={failedCount > 0 ? "destructive" : "outline"} className="font-mono">
              {failedCount}
            </Badge>
          </div>

          {updatedCount > 0 && (
            <div className="mt-4 text-xs text-green-600">
              ✓ Successfully added Google Place IDs to {updatedCount} leads
            </div>
          )}

          {failedCount > 0 && (
            <div className="mt-1 text-xs text-red-600">
              ⚠️ Could not find Google Place IDs for {failedCount} leads
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default PlaceIdSyncSummary;
