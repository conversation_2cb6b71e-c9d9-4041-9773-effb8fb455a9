import { NextResponse } from "next/server";
import { query } from "@/lib/db";
import {
  startOfWeek,
  startOfMonth,
  format,
  subMonths,
  parseISO,
} from "date-fns";

export async function GET() {
  try {
    console.log("Starting enhanced dashboard stats fetch...");

    // Get total leads count
    const totalLeadsResult = await query(
      'SELECT COUNT(*) as count FROM "bianchi_leads"'
    );
    const totalLeads = parseInt(totalLeadsResult[0]?.count || "0");
    console.log("Total leads:", totalLeads);

    // Get new leads this month
    const monthLeadsResult = await query(
      'SELECT COUNT(*) as count FROM "bianchi_leads" WHERE created_at >= $1',
      [startOfMonth(new Date()).toISOString()]
    );
    const newLeadsThisMonth = parseInt(monthLeadsResult[0]?.count || "0");
    console.log("New leads this month:", newLeadsThisMonth);

    // Get new leads this week
    const weekLeadsResult = await query(
      'SELECT COUNT(*) as count FROM "bianchi_leads" WHERE created_at >= $1',
      [startOfWeek(new Date()).toISOString()]
    );
    const newLeadsThisWeek = parseInt(weekLeadsResult[0]?.count || "0");
    console.log("New leads this week:", newLeadsThisWeek);

    // Get leads by source with detailed logging
    console.log("Fetching leads by source...");
    const sourceQuery = `
      SELECT COALESCE(source, 'Unknown') as source, COUNT(*) as count 
      FROM "bianchi_leads" 
      GROUP BY source 
      ORDER BY count DESC
    `;
    console.log("Source query:", sourceQuery);

    const leadsBySourceResult = await query(sourceQuery);
    console.log("Raw source results:", leadsBySourceResult);

    const leadsBySource = leadsBySourceResult.length
      ? leadsBySourceResult.map((row) => ({
          source: row.source,
          count: parseInt(row.count),
        }))
      : [
          { source: "PlacesAPI", count: 0 },
          { source: "ERP", count: 0 },
          { source: "Manual", count: 0 },
        ];
    console.log("Processed source data:", leadsBySource);

    // Get leads by city with logging
    console.log("Fetching leads by city...");
    const cityQuery = `
      SELECT COALESCE(city, 'Unknown') as city, COUNT(*) as count 
      FROM "bianchi_leads" 
      GROUP BY city 
      ORDER BY count DESC 
      LIMIT 10
    `;
    console.log("City query:", cityQuery);

    const leadsByCityResult = await query(cityQuery);
    console.log("Raw city results:", leadsByCityResult);

    const leadsByCity = leadsByCityResult.length
      ? leadsByCityResult
      : [{ city: "No data", count: 0 }];
    console.log("Processed city data:", leadsByCity);

    // Get leads over time with logging
    console.log("Fetching leads over time...");
    const timeQuery = `
      SELECT 
        DATE_TRUNC('month', created_at) as month,
        COUNT(*) as count
      FROM "bianchi_leads"
      WHERE created_at >= NOW() - INTERVAL '12 months'
      GROUP BY month
      ORDER BY month ASC
    `;
    console.log("Time query:", timeQuery);

    const leadsOverTimeResult = await query(timeQuery);
    console.log("Raw time results:", leadsOverTimeResult);

    const leadsOverTime = leadsOverTimeResult.length
      ? leadsOverTimeResult.map((row) => ({
          month: format(new Date(row.month), "MMM yyyy"),
          count: parseInt(row.count),
        }))
      : Array.from({ length: 12 }, (_, i) => ({
          month: format(
            new Date(new Date().setMonth(new Date().getMonth() - i)),
            "MMM yyyy"
          ),
          count: 0,
        })).reverse();
    console.log("Processed time data:", leadsOverTime);

    // Add growth metrics
    const lastMonthLeads = await query(
      `SELECT COUNT(*) as count FROM "bianchi_leads" 
       WHERE created_at >= $1 AND created_at < $2`,
      [
        subMonths(startOfMonth(new Date()), 1).toISOString(),
        startOfMonth(new Date()).toISOString(),
      ]
    );

    const thisMonthLeads = await query(
      `SELECT COUNT(*) as count FROM "bianchi_leads" 
       WHERE created_at >= $1`,
      [startOfMonth(new Date()).toISOString()]
    );

    const monthlyGrowth =
      (parseInt(thisMonthLeads[0]?.count || "0") /
        parseInt(lastMonthLeads[0]?.count || "1") -
        1) *
      100;

    // Add canton distribution
    const cantonDistribution = await query(
      `SELECT 
        canton,
        COUNT(*) as count,
        ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (), 1) as percentage
       FROM "bianchi_leads"
       GROUP BY canton
       ORDER BY count DESC
       LIMIT 5`
    );

    // Add business types insights
    const businessTypes = await query(
      `SELECT 
  INITCAP(REPLACE(type, '_', ' ')) as type,
  COUNT(*)::integer as count
 FROM (
   SELECT unnest(types) as type
   FROM "bianchi_leads"
 ) as expanded_types
 WHERE INITCAP(REPLACE(type, '_', ' ')) NOT IN ('Restaurant', 'Establishment', 'Food', 'Point Of Interest')
 GROUP BY type
 HAVING COUNT(*) > 5
 ORDER BY count DESC
 LIMIT 5`
    );

    // Ensure the counts are properly parsed
    const formattedBusinessTypes = businessTypes.map((row) => ({
      type: row.type,
      count: parseInt(row.count),
    }));

    // Add conversion potential (based on rating and review count)
    const highPotentialLeads = await query(
      `SELECT COUNT(*) as count
       FROM "bianchi_leads"
       WHERE rating >= 4.5 
       AND user_rating_count >= 100
       AND created_at >= $1`,
      [subMonths(new Date(), 1).toISOString()]
    );

    // Replace the leadsPerDay query with leadsPerMonth
    const leadsPerMonth = await query(
      `SELECT 
        DATE_TRUNC('month', created_at) as month,
        COUNT(*) as count
       FROM "bianchi_leads"
       WHERE created_at >= NOW() - INTERVAL '12 months'
       GROUP BY month
       ORDER BY month ASC`
    );

    const response = {
      totalLeads,
      newLeadsThisMonth,
      newLeadsThisWeek,
      monthlyGrowth: parseFloat(monthlyGrowth.toFixed(1)),
      leadsBySource,
      leadsByCity,
      leadsOverTime,
      cantonDistribution,
      businessTypes: formattedBusinessTypes,
      highPotentialLeads: parseInt(highPotentialLeads[0]?.count || "0"),
      leadsPerMonth: leadsPerMonth.map((row) => ({
        month: format(new Date(row.month), "MMM yyyy"),
        count: parseInt(row.count),
      })),
    };
    console.log("Final enhanced response:", response);

    return NextResponse.json(response);
  } catch (error) {
    console.error("Database error details:", error);
    return NextResponse.json(
      { error: "Failed to fetch dashboard statistics" },
      { status: 500 }
    );
  }
}
