import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import {
  getProgressData,
  debugProgressStore,
  ProgressData,
} from "../progress-utils";

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await req.json();
    const { searchId } = body;

    // Log the current state of the progress store for debugging
    console.log(
      `Progress check request received. SearchId: ${searchId || "none"}`
    );
    console.log(`Current progress store state: ${debugProgressStore()}`);

    // We no longer need dummy data as we're using real progress tracking
    // Log if no progress data is found
    const noProgressData = !getProgressData();
    if (noProgressData) {
      console.log("Progress store is empty. No active searches found.");
    }

    // Get progress data - first try with the provided search ID
    let progressData = getProgressData(searchId);

    // If no data found with the provided search ID, try to get the most recent progress data
    if (!progressData) {
      console.log(
        `No progress data found for searchId: ${
          searchId || "none"
        }, trying to get most recent data`
      );
      progressData = getProgressData(); // This will get the most recent entry

      if (progressData) {
        console.log(
          `Found most recent progress data: ${JSON.stringify(progressData)}`
        );
      } else {
        console.log(`No active searches found in the progress store`);

        // Return a default value with some progress to show something is happening
        return NextResponse.json(
          {
            progress: 5, // Start with 5% instead of 0% to show something is happening
            inProgress: 3,
            completed: 0,
            total: 100,
            message: "No active search found, but showing initial progress",
          },
          { status: 200 }
        );
      }
    }

    console.log(`Returning progress data: ${JSON.stringify(progressData)}`);

    // Return progress data
    return NextResponse.json({
      progress: progressData.progress,
      inProgress: progressData.inProgress,
      completed: progressData.completed,
      total: progressData.total,
      lastUpdated: progressData.lastUpdated,
    });
  } catch (error) {
    console.error("Error in progress check:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 }
    );
  }
}
