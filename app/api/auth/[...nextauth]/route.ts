import NextAuth from "next-auth";
import { authOptions } from "@/lib/auth"; // Import from the new location
import { neonConfig } from "@neondatabase/serverless";
import { compare } from "bcryptjs";
import PgAdapter from "@auth/pg-adapter";
import { Pool } from 'pg';

// Set up neon config for production
if (process.env.NODE_ENV === 'production') {
  // @ts-expect-error - neonConfig types are not correctly defined
  neonConfig.wsProxy = true;
  neonConfig.useSecureWebSocket = true;
}

// Create a PostgreSQL pool for all database operations
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: {
    rejectUnauthorized: false, // Required for Neon
  },
});

// Define custom types for next-auth
declare module "next-auth" {
  interface Session {
    user: {
      id?: string;
      name?: string | null;
      username?: string | null;
      image?: string | null;
      role?: string;
    }
  }

  interface User {
    id: string;
    name?: string | null;
    username?: string | null;
    image?: string | null;
    role?: string;
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    id?: string;
    role?: string;
    username?: string | null;
  }
}

// Create the Auth.js handler
const handler = NextAuth(authOptions);
export { handler as GET, handler as POST };

// Re-export authOptions for use elsewhere
// export { authOptions }; 