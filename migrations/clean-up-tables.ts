import { Pool } from 'pg';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Create a database connection
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: {
    rejectUnauthorized: false, // Required for Neon
  },
});

async function cleanupTables() {
  try {
    console.log('🧹 Starting database cleanup...');
    
    // Delete all existing data from auth tables to avoid foreign key issues
    console.log('Cleaning up auth-related tables...');
    
    // Process tables in reverse dependency order
    const tables = [
      'verification_tokens',
      'sessions',
      'accounts',
      'users'
    ];
    
    for (const table of tables) {
      try {
        // Check if table exists
        const tableExists = await pool.query(`
          SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_name = $1
          );
        `, [table]);
        
        if (tableExists.rows[0].exists) {
          try {
            // Try to drop the table - this is a fresh start approach
            await pool.query(`DROP TABLE IF EXISTS ${table} CASCADE;`);
            console.log(`✅ Dropped table: ${table}`);
          } catch (error) {
            console.error(`Error dropping table ${table}:`, error);
            
            // Fall back to truncating if dropping fails
            try {
              await pool.query(`DELETE FROM ${table};`);
              console.log(`✅ Deleted data from table: ${table}`);
            } catch (truncateError) {
              console.error(`Error deleting data from ${table}:`, truncateError);
            }
          }
        } else {
          console.log(`⏭️ Table doesn't exist, skipping: ${table}`);
        }
      } catch (error) {
        console.error(`Error processing table ${table}:`, error);
      }
    }
    
    console.log('✅ Database cleanup completed successfully!');
  } catch (error) {
    console.error('❌ Database cleanup failed:', error);
    process.exit(1);
  } finally {
    // Close the database connection
    await pool.end();
  }
}

// Run the cleanup if this file is executed directly
if (process.argv[1] === fileURLToPath(import.meta.url)) {
  cleanupTables()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('Cleanup script error:', error);
      process.exit(1);
    });
}

export { cleanupTables }; 