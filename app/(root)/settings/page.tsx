"use client"

import { useState } from "react"
import { toast } from "sonner"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { logActivity, LogAction } from "@/lib/logger"

interface SettingsState {
  apiKey: string
  scanFrequency: string
  notificationEmail: string
  emailNotifications: boolean
  slackWebhook: string
  slackNotifications: boolean
  autoSync: boolean
  syncFrequency: string
  defaultRadius: string
  maxResults: string
  language: string
  defaultView: string
  theme: string
}

type SettingsKey = keyof SettingsState;

export default function Settings() {
  const [settings, setSettings] = useState<SettingsState>({
    apiKey: "",
    scanFrequency: "daily",
    notificationEmail: "",
    emailNotifications: true,
    slackWebhook: "",
    slackNotifications: false,
    autoSync: true,
    syncFrequency: "daily",
    defaultRadius: "1000",
    maxResults: "20",
    language: "en",
    defaultView: "list",
    theme: "light",
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      // Here you would typically send this data to your backend
      // const response = await fetch("/api/settings", {
      //   method: "POST",
      //   headers: { "Content-Type": "application/json" },
      //   body: JSON.stringify(settings),
      // })
      console.log("Settings submitted:", settings)
      toast.success("Settings saved successfully")
    } catch (error) {
      console.error("Error saving settings:", error)
      toast.error("Failed to save settings")
    }
  }

  const handleSettingsUpdate = async (newSettings: SettingsState) => {
    try {
      await logActivity(
        LogAction.SETTINGS_UPDATE,
        "Settings updated successfully",
        {
          previous_settings: settings,
          new_settings: newSettings,
          changed_fields: (Object.keys(newSettings) as SettingsKey[]).filter(
            key => newSettings[key] !== settings[key]
          )
        }
      );

      setSettings(newSettings);
    } catch (error) {
      console.error("Error updating settings:", error);
      
      await logActivity(
        LogAction.SETTINGS_UPDATE,
        "Failed to update settings",
        {
          attempted_settings: newSettings,
          error: error instanceof Error ? error.message : "Unknown error"
        }
      );
    }
  };

  return (
    <div className="max-w-4xl mx-auto py-6 space-y-8">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* API Configuration */}
        <Card>
          <CardHeader>
            <CardTitle>API Configuration</CardTitle>
            <CardDescription>
              Configure your Google Maps API settings and scanning preferences
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="api-key">Google Maps API Key</Label>
              <Input
                id="api-key"
                value={settings.apiKey}
                onChange={(e) => setSettings({ ...settings, apiKey: e.target.value })}
                placeholder="Enter your API key"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="scan-frequency">Scan Frequency</Label>
              <Select
                value={settings.scanFrequency}
                onValueChange={(value) => setSettings({ ...settings, scanFrequency: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select frequency" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="hourly">Hourly</SelectItem>
                  <SelectItem value="daily">Daily</SelectItem>
                  <SelectItem value="weekly">Weekly</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Notification Settings */}
        <Card>
          <CardHeader>
            <CardTitle>Notification Preferences</CardTitle>
            <CardDescription>
              Configure how you want to receive notifications about new leads
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Email Notifications</Label>
                <p className="text-sm text-muted-foreground">
                  Receive notifications about new leads via email
                </p>
              </div>
              <Switch
                checked={settings.emailNotifications}
                onCheckedChange={(checked) =>
                  setSettings({ ...settings, emailNotifications: checked })
                }
              />
            </div>
            {settings.emailNotifications && (
              <div className="space-y-2">
                <Label htmlFor="notification-email">Notification Email</Label>
                <Input
                  id="notification-email"
                  type="email"
                  value={settings.notificationEmail}
                  onChange={(e) =>
                    setSettings({ ...settings, notificationEmail: e.target.value })
                  }
                  placeholder="Enter email address"
                />
              </div>
            )}
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Slack Notifications</Label>
                <p className="text-sm text-muted-foreground">
                  Receive notifications in your Slack channel
                </p>
              </div>
              <Switch
                checked={settings.slackNotifications}
                onCheckedChange={(checked) =>
                  setSettings({ ...settings, slackNotifications: checked })
                }
              />
            </div>
            {settings.slackNotifications && (
              <div className="space-y-2">
                <Label htmlFor="slack-webhook">Slack Webhook URL</Label>
                <Input
                  id="slack-webhook"
                  value={settings.slackWebhook}
                  onChange={(e) =>
                    setSettings({ ...settings, slackWebhook: e.target.value })
                  }
                  placeholder="Enter Slack webhook URL"
                />
              </div>
            )}
          </CardContent>
        </Card>

        {/* Data Synchronization */}
        <Card>
          <CardHeader>
            <CardTitle>Data Synchronization</CardTitle>
            <CardDescription>
              Configure how your lead data syncs with external systems
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Automatic Sync</Label>
                <p className="text-sm text-muted-foreground">
                  Automatically sync lead data with ERP system
                </p>
              </div>
              <Switch
                checked={settings.autoSync}
                onCheckedChange={(checked) =>
                  setSettings({ ...settings, autoSync: checked })
                }
              />
            </div>
            {settings.autoSync && (
              <div className="space-y-2">
                <Label htmlFor="sync-frequency">Sync Frequency</Label>
                <Select
                  value={settings.syncFrequency}
                  onValueChange={(value) =>
                    setSettings({ ...settings, syncFrequency: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select frequency" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="hourly">Hourly</SelectItem>
                    <SelectItem value="daily">Daily</SelectItem>
                    <SelectItem value="weekly">Weekly</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Search Preferences */}
        <Card>
          <CardHeader>
            <CardTitle>Search Preferences</CardTitle>
            <CardDescription>
              Configure default settings for lead generation searches
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="default-radius">Default Search Radius (meters)</Label>
              <Input
                id="default-radius"
                type="number"
                value={settings.defaultRadius}
                onChange={(e) =>
                  setSettings({ ...settings, defaultRadius: e.target.value })
                }
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="max-results">Maximum Results per Search</Label>
              <Input
                id="max-results"
                type="number"
                value={settings.maxResults}
                onChange={(e) =>
                  setSettings({ ...settings, maxResults: e.target.value })
                }
              />
            </div>
          </CardContent>
        </Card>

        {/* Display Preferences */}
        <Card>
          <CardHeader>
            <CardTitle>Display Preferences</CardTitle>
            <CardDescription>
              Configure how you want the application to look and behave
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="language">Language</Label>
              <Select
                value={settings.language}
                onValueChange={(value) => setSettings({ ...settings, language: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select language" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="en">English</SelectItem>
                  <SelectItem value="de">German</SelectItem>
                  <SelectItem value="fr">French</SelectItem>
                  <SelectItem value="it">Italian</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="default-view">Default View</Label>
              <Select
                value={settings.defaultView}
                onValueChange={(value) => setSettings({ ...settings, defaultView: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select default view" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="list">List View</SelectItem>
                  <SelectItem value="grid">Grid View</SelectItem>
                  <SelectItem value="map">Map View</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="theme">Theme</Label>
              <Select
                value={settings.theme}
                onValueChange={(value) => setSettings({ ...settings, theme: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select theme" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="light">Light</SelectItem>
                  <SelectItem value="dark">Dark</SelectItem>
                  <SelectItem value="system">System</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-end">
          <Button type="submit" size="lg">
            Save Settings
          </Button>
        </div>
      </form>
    </div>
  )
}

