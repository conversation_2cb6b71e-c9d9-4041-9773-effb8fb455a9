import { sql } from "@vercel/postgres";

export async function createLeadsTable() {
  try {
    await sql`
      CREATE TABLE IF NOT EXISTS "bianchi_leads" (
        id SERIAL PRIMARY KEY,
        source VARCHAR(50) NOT NULL,
        google_place_id VARCHAR(255),
        erp_id VARCHAR(255) UNIQUE,
        name VARCHAR(255) NOT NULL,
        formatted_address VARCHAR(255),
        address_components JSONB,
        street_name VARCHAR(255),
        street_number VARCHAR(50),
        city VARCHAR(100),
        postal_code VARCHAR(20),
        canton VARCHAR(100),
        country VARCHAR(100),
        international_phone VARCHAR(50),
        national_phone VARCHAR(50),
        website_uri <PERSON>RCHAR(255),
        business_status VARCHAR(50),
        price_level INTEGER,
        rating NUMERIC(3, 2),
        user_rating_count INTEGER,
        reviews JSONB,
        opening_hours JSONB,
        types TEXT[],
        location GEOGRAPHY(POINT, 4326),
        plus_code VARCHAR(50),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `;

    // Create indexes
    await sql`CREATE UNIQUE INDEX IF NOT EXISTS bianchi_leads_pkey ON "bianchi_leads" USING BTREE (id)`;
    await sql`CREATE UNIQUE INDEX IF NOT EXISTS bianchi_leads_erp_id_key ON "bianchi_leads" USING BTREE (erp_id)`;
    await sql`CREATE INDEX IF NOT EXISTS bianchi_leads_name_idx ON "bianchi_leads" USING BTREE (name)`;
    await sql`CREATE INDEX IF NOT EXISTS bianchi_leads_canton_idx ON "bianchi_leads" USING BTREE (canton)`;
    await sql`CREATE INDEX IF NOT EXISTS bianchi_leads_google_place_id_idx ON "bianchi_leads" USING BTREE (google_place_id)`;

    // Note: Using double quotes for table name because it contains a hyphen
    await sql`
      INSERT INTO "bianchi_leads" (
        name, 
        source,
        canton, 
        formatted_address, 
        city, 
        country,
        street_name,
        street_number,
        postal_code,
        types
      ) VALUES 
        ('John Doe', 'Manual Entry', 'Zurich', 'Bahnhofstrasse 1, 8001 Zürich', 'Zürich', 'Switzerland', 'Bahnhofstrasse', '1', '8001', ARRAY['restaurant']),
        ('Jane Smith', 'Manual Entry', 'Geneva', 'Rue du Rhône 10, 1204 Genève', 'Genève', 'Switzerland', 'Rue du Rhône', '10', '1204', ARRAY['cafe']),
        ('Max Müller', 'Manual Entry', 'Bern', 'Bundesplatz 1, 3011 Bern', 'Bern', 'Switzerland', 'Bundesplatz', '1', '3011', ARRAY['store'])
      ON CONFLICT DO NOTHING
    `;

    console.log("Leads table initialized successfully");
  } catch (error) {
    console.error("Leads table initialization error:", error);
    throw error;
  }
}

export async function createUsersTable() {
  try {
    await sql`
      CREATE TABLE IF NOT EXISTS "users" (
        id SERIAL PRIMARY KEY,
        username VARCHAR(255) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        role VARCHAR(50) NOT NULL CHECK (role IN ('admin', 'full_access', 'limited_access')),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `;

    // Insert default admin user if not exists
    await sql`
      INSERT INTO "users" (username, password, role)
      VALUES 
        ('admin', 'admin123', 'admin')
      ON CONFLICT (username) DO NOTHING
    `;
    
    // Create indexes for better performance
    await sql`CREATE INDEX IF NOT EXISTS idx_users_username ON "users" (username)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_users_role ON "users" (role)`;

    console.log("Users table initialized successfully");
  } catch (error) {
    console.error("Users table initialization error:", error);
    throw error;
  }
}
