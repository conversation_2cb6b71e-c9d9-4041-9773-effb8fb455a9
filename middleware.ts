import { NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import type { NextRequest } from "next/server";

// Configure paths that don't require authentication
const publicPaths = ["/login", "/api/auth"];

// Configure paths that require admin role
const adminPaths = ["/users", "/api/users", "/admin", "/lead-generator", "/logs", "/settings"];

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Check if the path is public
  if (publicPaths.some(path => pathname.startsWith(path))) {
    return NextResponse.next();
  }
  
  // Verify authentication
  const token = await getToken({ 
    req: request,
    secret: process.env.NEXTAUTH_SECRET
  });
  
  // If not authenticated, redirect to login
  if (!token) {
    const loginUrl = new URL("/login", request.url);
    return NextResponse.redirect(loginUrl);
  }
  
  // If trying to access admin routes, check admin role
  if (adminPaths.some(path => pathname.startsWith(path))) {
    if (token.role !== "admin") {
      // Redirect to the unauthorized page (route groups don't affect URL path)
      return NextResponse.redirect(new URL("/unauthorized", request.url));
    }
  }
  
  return NextResponse.next();
}

export const config = {
  matcher: ["/((?!_next/static|_next/image|favicon.ico).*)"],
}; 