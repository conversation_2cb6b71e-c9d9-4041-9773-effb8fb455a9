// Global variable to track search progress across requests
// This is a simple in-memory store that will be reset when the server restarts
export interface ProgressData {
  progress: number;
  inProgress: number;
  completed: number;
  total: number;
  lastUpdated: number;
}

// Use a Map to store progress data for different search sessions
const progressStore = new Map<string, ProgressData>();

// Function to get progress data
export function getProgressData(searchId?: string): ProgressData | null {
  // If no searchId is provided, return the most recent progress data
  if (!searchId) {
    let mostRecent: ProgressData | null = null;
    let mostRecentTimestamp = 0;

    for (const data of progressStore.values()) {
      if (data.lastUpdated > mostRecentTimestamp) {
        mostRecentTimestamp = data.lastUpdated;
        mostRecent = data;
      }
    }
    return mostRecent;
  }

  // Otherwise, return the progress data for the specified searchId
  return progressStore.get(searchId) || null;
}

// Function to update progress data
export function updateProgressData(
  progress: number,
  inProgress: number,
  completed: number,
  total: number,
  searchId?: string
): void {
  const now = Date.now();
  const data: ProgressData = {
    progress,
    inProgress,
    completed,
    total,
    lastUpdated: now,
  };

  // If no searchId is provided, use a default key
  const key = searchId || "default_search";
  progressStore.set(key, data);

  // Clean up old entries (older than 1 hour)
  const oneHourAgo = now - 60 * 60 * 1000;
  for (const [id, storedData] of progressStore.entries()) {
    if (storedData.lastUpdated < oneHourAgo) {
      progressStore.delete(id);
    }
  }
}

// Add a debug function to log the current state of the progress store
export function debugProgressStore(): string {
  const entries = Array.from(progressStore.entries());
  return JSON.stringify(
    {
      storeSize: progressStore.size,
      entries: entries.map(([key, data]) => ({
        key,
        progress: data.progress,
        completed: data.completed,
        total: data.total,
        lastUpdated: new Date(data.lastUpdated).toISOString(),
      })),
    },
    null,
    2
  );
}

// Add a dummy progress entry for testing if needed
export function addDummyProgress() {
  if (progressStore.size === 0) {
    updateProgressData(25, 2, 5, 20, "dummy_search");
    console.log("Added dummy progress data for testing");
  }
}
