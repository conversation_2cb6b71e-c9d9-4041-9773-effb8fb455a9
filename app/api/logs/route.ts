import { NextResponse } from "next/server";
import { query } from "@/lib/db";
import { headers } from 'next/headers';

export async function POST(request: Request) {
  try {
    const { action, description, metadata } = await request.json();
    const headersList = await headers();
    const user_ip = await headersList.get('x-forwarded-for') || request.headers.get('x-forwarded-for') || 'unknown';
    const user_agent = await headersList.get('user-agent') || request.headers.get('user-agent') || 'unknown';

    const result = await query(
      `INSERT INTO "bianchi_logs" 
       (action, description, user_ip, user_agent, metadata) 
       VALUES ($1, $2, $3, $4, $5)
       RETURNING id`,
      [action, description, user_ip, user_agent, JSON.stringify(metadata)]
    );

    return NextResponse.json({ success: true, id: result[0].id });
  } catch (error) {
    console.error("Error logging activity:", error);
    return NextResponse.json(
      { error: "Failed to log activity" },
      { status: 500 }
    );
  }
}

export async function GET(request: Request) {
  try {
    const searchParams = new URL(request.url).searchParams;
    const limit = parseInt(searchParams.get('limit') || '100');
    const offset = parseInt(searchParams.get('offset') || '0');
    const action = searchParams.get('action');

    const sql = `
      SELECT * FROM "bianchi_logs"
      ${action ? 'WHERE action = $3' : ''}
      ORDER BY created_at DESC
      LIMIT $1 OFFSET $2
    `;

    const params = action ? [limit, offset, action] : [limit, offset];
    const logs = await query(sql, params);

    return NextResponse.json({ logs });
  } catch (error) {
    console.error("Error fetching logs:", error);
    return NextResponse.json(
      { error: "Failed to fetch logs" },
      { status: 500 }
    );
  }
} 