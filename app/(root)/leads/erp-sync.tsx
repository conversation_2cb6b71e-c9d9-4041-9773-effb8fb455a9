"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { AlertCircle, Database } from "lucide-react";
import { Lead } from "@/types/lead";
import PlaceIdSyncSummary from "@/components/PlaceIdSyncSummary";

// Field mapping from ERP (German) to Lead (English)
const fieldMapping: Record<string, keyof Lead> = {
  freigegeben: "approved",
  AdressNr: "erp_id",
  SprachCode: "language_code",
  Sprache_Bezeichnung: "language_description",
  Name1: "name1",
  Name2: "name2",
  Name3: "name3",
  Strasse: "street_name", // This will be processed specially
  Plz: "postal_code",
  Ort: "city", // This will be processed specially to extract canton
  Telefon: "national_phone",
  Email: "email",
  Email_Ansprechpartner: "contact_person_email",
  adressgruppe: "address_group",
  adressgruppen_bezeichnung: "address_group_description",
  Vertreter1: "representative1",
  Vertreter2: "representative2",
  Obergruppe: "parent_group",
  Obergruppe_bez: "parent_group_description",
  Gruppe: "group",
  Gruppe_bez: "group_description",
  Betriebsart: "business_type",
  Betriebsart_bez: "business_type_description",
  Anrede_Nr: "salutation_number",
  Anrede_bez: "salutation_description",
  vorname_ansprechpartner: "contact_person_first_name",
  name_ansprechpartner: "contact_person_last_name",
};

// Extract street name and number from a combined string
const extractStreetComponents = (
  fullStreet: string
): { streetName: string; streetNumber: string } => {
  // Match pattern for street number at the end, potentially with a letter (like 11A)
  const match = fullStreet.match(/^(.*?)\s+(\d+\w*)$/);

  if (match) {
    return {
      streetName: match[1].trim(),
      streetNumber: match[2].trim(),
    };
  }

  // If no match, return the full string as street name and empty string as number
  return {
    streetName: fullStreet,
    streetNumber: "",
  };
};

// Extract city and canton from a combined string
const extractCityComponents = (
  fullCity: string
): { city: string; canton: string } => {
  // Look for uppercase words (cantons) at the end of the string
  const match = fullCity.match(/^(.*?)(?:\s+([A-Z]{2,})\s*)?$/);

  if (match && match[2]) {
    return {
      city: match[1].trim(),
      canton: match[2].trim(),
    };
  }

  // If no match or no canton part, return the full string as city
  return {
    city: fullCity,
    canton: "",
  };
};

// Convert string values from CSV to appropriate types
const convertValue = (key: keyof Lead, value: string): any => {
  if (key === "approved") {
    // J = true, N = false (German Ja/Nein)
    if (
      value === "J" ||
      value.toLowerCase() === "ja" ||
      value === "1" ||
      value.toLowerCase() === "true"
    ) {
      return true;
    }
    if (
      value === "N" ||
      value.toLowerCase() === "nein" ||
      value === "0" ||
      value.toLowerCase() === "false"
    ) {
      return false;
    }
    // If it's neither clearly true nor false, return null
    return null;
  }

  // Return string values as is
  return value;
};

// Parse CSV data into Lead objects
const parseCSV = (csvText: string): Lead[] => {
  // Split the CSV text into lines
  const lines = csvText.split(/\r?\n/).filter((line) => line.trim());

  // The first line is the header
  const headers = lines[0].split(";");

  // Map each line to a Lead object
  return lines.slice(1).map((line, index) => {
    const values = line.split(";");
    const lead: Partial<Lead> = {
      id: -(index + 1), // Use negative IDs for preview to avoid conflicts
      source: "ERP",
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      country: "Switzerland", // Default to Switzerland
    };

    // Special handling for street (to extract street_name and street_number)
    let fullStreetAddress = "";

    // Special handling for city (to extract city and canton)
    let fullCity = "";

    // Map each CSV column to the corresponding Lead field
    headers.forEach((header, i) => {
      const mappedField = fieldMapping[header];
      if (mappedField && values[i]) {
        // Special handling for street
        if (mappedField === "street_name") {
          fullStreetAddress = values[i];
        }
        // Special handling for city
        else if (mappedField === "city") {
          fullCity = values[i];
        }
        // Normal handling for all other fields
        else {
          lead[mappedField] = convertValue(mappedField, values[i]);
        }
      }
    });

    // Process street components if street was provided
    if (fullStreetAddress) {
      const { streetName, streetNumber } =
        extractStreetComponents(fullStreetAddress);
      lead.street_name = streetName;
      lead.street_number = streetNumber;
    }

    // Process city components if city was provided
    if (fullCity) {
      const { city, canton } = extractCityComponents(fullCity);
      lead.city = city;
      lead.canton = canton;
    }

    // Set name from name1 if name is not set
    if (!lead.name && lead.name1) {
      lead.name = lead.name1;
    }

    // Construct formatted address
    const addressParts = [];
    if (lead.street_name) {
      if (lead.street_number) {
        addressParts.push(`${lead.street_name} ${lead.street_number}`);
      } else {
        addressParts.push(lead.street_name);
      }
    }

    if (lead.postal_code || lead.city) {
      const cityPart = [lead.postal_code, lead.city].filter(Boolean).join(" ");
      if (cityPart) addressParts.push(cityPart);
    }

    if (lead.canton) {
      addressParts.push(lead.canton);
    }

    if (lead.country) {
      addressParts.push(lead.country);
    }

    if (addressParts.length > 0) {
      lead.formatted_address = addressParts.join(", ");
    }

    // If phone number is provided but international phone isn't,
    // prepend Swiss country code to create international format
    if (lead.national_phone && !lead.international_phone) {
      // Remove leading 0 if present and add +41 Swiss country code
      lead.international_phone = "+41" + lead.national_phone.replace(/^0+/, "");
    }

    return lead as Lead;
  });
};

export default function ERPSync() {
  const [csvFile, setCsvFile] = useState<File | null>(null);
  const [parsedLeads, setParsedLeads] = useState<Lead[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [importing, setImporting] = useState(false);
  const [importComplete, setImportComplete] = useState(false);
  const [importStats, setImportStats] = useState({
    total: 0,
    success: 0,
    errors: 0,
  });
  const [existingLeads, setExistingLeads] = useState<string[]>([]);
  const [checkingExisting, setCheckingExisting] = useState(false);

  // Google Place ID sync state
  const [placeIdSyncState, setPlaceIdSyncState] = useState({
    isLoading: false,
    updatedCount: 0,
    failedCount: 0,
    totalCount: 0,
    progress: 0,
    importedLeadIds: [] as number[],
  });

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setError(null);
    setParsedLeads([]);
    setImportComplete(false);
    setExistingLeads([]);

    const files = e.target.files;
    if (!files || files.length === 0) {
      return;
    }

    const file = files[0];
    if (file.type !== "text/csv" && !file.name.endsWith(".csv")) {
      setError("Please select a CSV file");
      return;
    }

    setCsvFile(file);

    // Read the file content
    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const csvText = e.target?.result as string;
        const leads = parseCSV(csvText);
        setParsedLeads(leads);

        // Check which leads already exist in the database
        await checkExistingLeads(leads);
      } catch (err) {
        setError("Failed to parse CSV file. Please check the format.");
        console.error(err);
      }
    };
    reader.onerror = () => {
      setError("Failed to read the file");
    };

    reader.readAsText(file);
  };

  // Check which leads already exist in the database
  const checkExistingLeads = async (leads: Lead[]) => {
    setCheckingExisting(true);
    try {
      // Get all ERP IDs from parsed leads
      const erpIds = leads.map((lead) => lead.erp_id).filter(Boolean);

      if (erpIds.length === 0) {
        setCheckingExisting(false);
        return;
      }

      // Send request to check which ERP IDs already exist
      const response = await fetch("/api/leads/check-existing", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ erpIds }),
      });

      if (!response.ok) {
        throw new Error("Failed to check existing leads");
      }

      const result = await response.json();
      setExistingLeads(result.existingIds || []);
    } catch (err) {
      console.error("Error checking existing leads:", err);
      // Don't set an error, just log it - the import can still proceed
    } finally {
      setCheckingExisting(false);
    }
  };

  const handleImport = async () => {
    if (!parsedLeads.length) return;

    setImporting(true);
    setError(null);

    try {
      const response = await fetch("/api/leads/import", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ leads: parsedLeads }),
      });

      if (!response.ok) {
        throw new Error("Failed to import leads");
      }

      const result = await response.json();
      setImportStats({
        total: parsedLeads.length,
        success: result.imported || 0,
        errors: parsedLeads.length - (result.imported || 0),
      });
      setImportComplete(true);

      // If leads were successfully imported, fetch Google Place IDs for them
      if (result.imported > 0 && result.leadIds && result.leadIds.length > 0) {
        await fetchGooglePlaceIds(result.leadIds);
      }
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "An unknown error occurred"
      );
    } finally {
      setImporting(false);
    }
  };

  // Fetch Google Place IDs for imported leads
  const fetchGooglePlaceIds = async (leadIds: number[]) => {
    if (!leadIds.length) return;

    // Reset place ID sync state
    setPlaceIdSyncState({
      isLoading: true,
      updatedCount: 0,
      failedCount: 0,
      totalCount: leadIds.length,
      progress: 0,
      importedLeadIds: leadIds,
    });

    try {
      const response = await fetch("/api/leads/batch-update-place-ids", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ leadIds }),
      });

      if (!response.ok) {
        throw new Error("Failed to fetch Google Place IDs");
      }

      const result = await response.json();

      setPlaceIdSyncState({
        isLoading: false,
        updatedCount: result.updated || 0,
        failedCount: result.failed || 0,
        totalCount: result.total || 0,
        progress: 100, // Complete
        importedLeadIds: leadIds,
      });
    } catch (err) {
      console.error("Error fetching Google Place IDs:", err);

      setPlaceIdSyncState((prev) => ({
        ...prev,
        isLoading: false,
        progress: 100, // Complete with error
      }));
    }
  };

  const resetForm = () => {
    setCsvFile(null);
    setParsedLeads([]);
    setError(null);
    setImportComplete(false);
    setExistingLeads([]);
  };

  // Calculate import summary stats
  const newLeadsCount = parsedLeads.filter(
    (lead) => lead.erp_id && !existingLeads.includes(lead.erp_id)
  ).length;
  const updateLeadsCount = parsedLeads.filter(
    (lead) => lead.erp_id && existingLeads.includes(lead.erp_id)
  ).length;
  const missingErpIdCount = parsedLeads.filter((lead) => !lead.erp_id).length;

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-2xl font-bold mb-6">ERP Data Synchronization</h1>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Import Leads from ERP CSV</CardTitle>
          <CardDescription>
            Upload a CSV file exported from your ERP system to import leads into
            the database. The CSV should use semicolons (;) as separators and
            include the German field names.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <div className="grid w-full max-w-sm items-center gap-1.5">
              <Input
                id="erp-csv"
                type="file"
                accept=".csv"
                onChange={handleFileChange}
                className="cursor-pointer"
                disabled={importing || checkingExisting}
              />
            </div>
            <Button
              variant="outline"
              onClick={resetForm}
              disabled={!csvFile || importing || checkingExisting}
            >
              Reset
            </Button>
          </div>
        </CardContent>
      </Card>

      {checkingExisting && (
        <div className="mb-8 p-4 text-center border rounded-md bg-muted">
          Checking database for existing leads...
        </div>
      )}

      {error && (
        <Alert variant="destructive" className="mb-8">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {parsedLeads.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Preview Import Data</CardTitle>
            <CardDescription>
              Found {parsedLeads.length} leads in the CSV file. Review the data
              before importing.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Street</TableHead>
                    <TableHead>Number</TableHead>
                    <TableHead>City</TableHead>
                    <TableHead>Canton</TableHead>
                    <TableHead>PLZ</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Phone</TableHead>
                    <TableHead>ERP ID</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {parsedLeads.slice(0, 10).map((lead, i) => (
                    <TableRow key={i}>
                      <TableCell>{lead.name || lead.name1 || "—"}</TableCell>
                      <TableCell>{lead.street_name || "—"}</TableCell>
                      <TableCell>{lead.street_number || "—"}</TableCell>
                      <TableCell>{lead.city || "—"}</TableCell>
                      <TableCell>{lead.canton || "—"}</TableCell>
                      <TableCell>{lead.postal_code || "—"}</TableCell>
                      <TableCell>{lead.email || "—"}</TableCell>
                      <TableCell>{lead.national_phone || "—"}</TableCell>
                      <TableCell>{lead.erp_id || "—"}</TableCell>
                      <TableCell>
                        {!lead.erp_id ? (
                          <span className="text-red-500">Missing ERP ID</span>
                        ) : existingLeads.includes(lead.erp_id as string) ? (
                          <span className="text-amber-500">Update</span>
                        ) : (
                          <span className="text-green-500">New</span>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
                {parsedLeads.length > 10 && (
                  <TableCaption>
                    Showing 10 of {parsedLeads.length} records
                  </TableCaption>
                )}
              </Table>
            </div>

            {!checkingExisting && parsedLeads.length > 0 && (
              <div className="mt-6 p-4 border rounded-md bg-muted">
                <h3 className="text-lg font-medium mb-2">Import Summary</h3>
                <ul className="space-y-1">
                  <li className="flex justify-between">
                    <span>Total leads in CSV:</span>
                    <span className="font-medium">{parsedLeads.length}</span>
                  </li>
                  <li className="flex justify-between">
                    <span>New leads to be imported:</span>
                    <span className="font-medium text-green-500">
                      {newLeadsCount}
                    </span>
                  </li>
                  <li className="flex justify-between">
                    <span>Existing leads to be updated:</span>
                    <span className="font-medium text-amber-500">
                      {updateLeadsCount}
                    </span>
                  </li>
                  {missingErpIdCount > 0 && (
                    <li className="flex justify-between">
                      <span>Leads with missing ERP ID (will be skipped):</span>
                      <span className="font-medium text-red-500">
                        {missingErpIdCount}
                      </span>
                    </li>
                  )}
                </ul>
              </div>
            )}
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" onClick={resetForm} disabled={importing}>
              Cancel
            </Button>
            <Button
              onClick={handleImport}
              disabled={importing || importComplete || checkingExisting}
            >
              {importing ? "Importing..." : "Import All Leads"}
            </Button>
          </CardFooter>
        </Card>
      )}

      {importComplete && (
        <Alert
          className="mt-8"
          variant={importStats.errors ? "destructive" : "default"}
        >
          <Database className="h-4 w-4" />
          <AlertTitle>Import Complete</AlertTitle>
          <AlertDescription>
            Imported {importStats.success} of {importStats.total} leads
            {importStats.errors > 0 && ` (${importStats.errors} failed)`}.
          </AlertDescription>
        </Alert>
      )}

      {/* Google Place ID Sync Summary */}
      {(placeIdSyncState.isLoading || placeIdSyncState.totalCount > 0) && (
        <div className="mt-8">
          <PlaceIdSyncSummary
            updatedCount={placeIdSyncState.updatedCount}
            failedCount={placeIdSyncState.failedCount}
            totalCount={placeIdSyncState.totalCount}
            isLoading={placeIdSyncState.isLoading}
            progress={placeIdSyncState.progress}
          />
        </div>
      )}
    </div>
  );
}
