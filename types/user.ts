export type UserRole = 'admin' | 'user';

export interface User {
  id: string;
  name?: string | null;
  username?: string | null;
  emailVerified?: Date | null;
  image?: string | null;
  role?: UserRole;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface UserWithPassword extends User {
  password: string;
}

export interface UserSession {
  id: string;
  name?: string | null;
  username?: string | null;
  role?: UserRole;
  sessionId: string;
  createdAt: string;
} 