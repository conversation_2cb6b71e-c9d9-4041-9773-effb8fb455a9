import { NextResponse } from "next/server";
import { query } from "@/lib/db";

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { places, source } = body;

    const addedLeads = [];
    
    for (const place of places) {
      // Check if lead already exists by Google Place ID or ERP ID
      const existing = await query(
        'SELECT id FROM "bianchi_leads" WHERE google_place_id = $1 OR erp_id = $2',
        [place.place_id, place.erp_id]
      );

      if (existing.length === 0) {
        // Add new lead with all available data
        const result = await query(
          `INSERT INTO "bianchi_leads" (
            source,
            google_place_id,
            erp_id,
            name,
            formatted_address,
            street_name,
            street_number,
            city,
            postal_code,
            canton,
            country,
            business_status,
            location,
            types,
            address_components,
            created_at,
            updated_at
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, ST_SetSRID(ST_MakePoint($13, $14), 4326), $15::text[], $16::jsonb, NOW(), NOW()) RETURNING id`,
          [
            source,
            place.place_id,
            place.erp_id,
            place.name,
            place.formatted_address,
            place.street_name,
            place.street_number,
            place.city,
            place.postal_code,
            place.canton,
            place.country,
            place.business_status,
            place.location?.longitude,
            place.location?.latitude,
            place.types || [],
            JSON.stringify(place.address_components || {})
          ]
        );
        
        addedLeads.push(result[0].id);
      }
    }

    return NextResponse.json({ 
      success: true, 
      added: addedLeads.length,
      message: `Added ${addedLeads.length} new leads` 
    });
  } catch (error) {
    console.error("Database error:", error);
    return NextResponse.json(
      { error: "Failed to add leads" },
      { status: 500 }
    );
  }
} 