import { Pool } from 'pg';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Load environment variables from .env.local
dotenv.config({ path: '.env.local' });

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Execute SQL statements safely, one at a time
 */
async function executeSqlStatements(client: Pool, sql: string, description: string) {
  // Split the SQL file into individual statements
  const statements = sql
    .split(';')
    .map(stmt => stmt.trim())
    .filter(stmt => stmt.length > 0);
  
  for (const statement of statements) {
    try {
      await client.query(`${statement};`);
    } catch (error) {
      console.error(`Error in ${description}:`, error);
      throw error;
    }
  }
  
  console.log(`✅ ${description} completed successfully`);
}

/**
 * Create Auth.js tables for NextAuth.js with Neon adapter
 */
export async function createAuthTables(client: Pool) {
  const sql = `
    -- Users table
    CREATE TABLE IF NOT EXISTS "users" (
      id TEXT PRIMARY KEY,
      name TEXT,
      username TEXT UNIQUE,
      email_verified TIMESTAMP WITH TIME ZONE,
      image TEXT,
      password TEXT,
      role TEXT DEFAULT 'user',
      created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
    );

    -- Accounts table (for OAuth providers)
    CREATE TABLE IF NOT EXISTS "accounts" (
      id TEXT PRIMARY KEY,
      user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      type TEXT NOT NULL,
      provider TEXT NOT NULL,
      provider_account_id TEXT NOT NULL,
      refresh_token TEXT,
      access_token TEXT,
      expires_at BIGINT,
      token_type TEXT,
      scope TEXT,
      id_token TEXT,
      session_state TEXT,
      UNIQUE(provider, provider_account_id)
    );

    -- Sessions table
    CREATE TABLE IF NOT EXISTS "sessions" (
      id TEXT PRIMARY KEY,
      session_token TEXT NOT NULL UNIQUE,
      user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      expires TIMESTAMP WITH TIME ZONE NOT NULL
    );

    -- Verification tokens table (for email verification)
    CREATE TABLE IF NOT EXISTS "verification_tokens" (
      identifier TEXT NOT NULL,
      token TEXT NOT NULL,
      expires TIMESTAMP WITH TIME ZONE NOT NULL,
      PRIMARY KEY (identifier, token)
    );
  `;

  await executeSqlStatements(client, sql, "Auth.js tables creation");
}

/**
 * Run the migration
 */
export async function runMigration() {
  const client = new Pool({
    connectionString: process.env.DATABASE_URL,
    ssl: {
      rejectUnauthorized: false,
    },
  });

  try {
    console.log("🔍 Starting Auth.js tables migration...");
    
    // Set application_name for better visibility in Neon console
    await client.query("SET application_name = 'auth_tables_migration'");
    
    // Create Auth.js tables
    await createAuthTables(client);
    
    console.log("✅ Migration completed successfully!");
  } catch (error) {
    console.error("❌ Migration failed:", error);
    process.exit(1);
  } finally {
    await client.end();
  }
}

// Run the migration if this file is executed directly
if (process.argv[1] === fileURLToPath(import.meta.url)) {
  runMigration()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error("Migration script error:", error);
      process.exit(1);
    });
} 