import { UserManagement } from "@/components/user-management";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { redirect } from "next/navigation";
import { Pool } from "pg";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";

// Create a database connection pool
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: {
    rejectUnauthorized: false, // Required for Neon
  },
});

export default async function UsersPage() {
  // Check if the user is authenticated and is an admin
  const session = await getServerSession(authOptions);
  
  if (!session || session.user.role !== "admin") {
    redirect('/unauthorized');
  }
  
  // Get all users from the database
  const result = await pool.query(`
    SELECT id, name, username, image, role, created_at, updated_at 
    FROM users 
    ORDER BY name ASC
  `);
  
  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">User Management</h1>
        <Link href="/" passHref>
          <Button variant="outline">Back to Dashboard</Button>
        </Link>
      </div>
      <UserManagement users={result.rows} />
    </div>
  );
} 