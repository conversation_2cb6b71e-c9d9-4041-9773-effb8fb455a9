import { NextResponse } from "next/server";
import { query } from "@/lib/db";
import { areSimilar } from "@/lib/string-similarity";

const GOOGLE_MAPS_API_KEY = process.env.GOOGLE_MAPS_API_KEY;
const PLACES_API_URL = "https://places.googleapis.com/v1/places:searchText";

// Delay function to avoid hitting rate limits
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { leadIds } = body;

    // If no lead IDs provided, find all leads without Google Place IDs
    let leadsToUpdate = [];

    if (!leadIds || leadIds.length === 0) {
      leadsToUpdate = await query(
        `SELECT id, name, street_name, city, country
         FROM "bianchi_leads"
         WHERE google_place_id IS NULL OR google_place_id = ''
         LIMIT 100`
      );
    } else {
      leadsToUpdate = await query(
        `SELECT id, name, street_name, city, country
         FROM "bianchi_leads"
         WHERE id = ANY($1::int[])`,
        [leadIds]
      );
    }

    if (leadsToUpdate.length === 0) {
      return NextResponse.json({
        message: "No leads to update",
        updated: 0,
        failed: 0,
        total: 0,
        details: [],
      });
    }

    interface ResultDetail {
      id: number;
      name: string;
      success: boolean;
      message?: string;
      place_id?: string;
      googleName?: string | null;
    }

    const results = {
      updated: 0,
      failed: 0,
      total: leadsToUpdate.length,
      details: [] as ResultDetail[],
    };

    // Process each lead
    for (const lead of leadsToUpdate) {
      try {
        // Construct search query using available information
        const searchQuery = [
          lead.name,
          lead.street_name,
          lead.city,
          lead.country,
        ]
          .filter(Boolean)
          .join(", ");

        if (!searchQuery) {
          results.failed++;
          results.details.push({
            id: lead.id,
            name: lead.name,
            success: false,
            message: "Insufficient data for search query",
          });
          continue;
        }

        const response = await fetch(PLACES_API_URL, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-Goog-Api-Key": GOOGLE_MAPS_API_KEY!,
            "X-Goog-FieldMask": "places.id,places.displayName",
          },
          body: JSON.stringify({
            textQuery: searchQuery,
            maxResultCount: 1,
          }),
        });

        if (!response.ok) {
          console.error("Places API error:", await response.text());
          results.failed++;
          results.details.push({
            id: lead.id,
            name: lead.name,
            success: false,
            message: `API error: ${response.statusText}`,
          });
          continue;
        }

        const data = await response.json();
        const place = data.places?.[0];
        const googleName = place?.displayName?.text;

        // Only update if the names are similar enough
        if (place && googleName && areSimilar(lead.name, googleName)) {
          // Update the lead with the Google Place ID
          await query(
            `UPDATE "bianchi_leads"
             SET google_place_id = $1, updated_at = CURRENT_TIMESTAMP
             WHERE id = $2`,
            [place.id, lead.id]
          );

          results.updated++;
          results.details.push({
            id: lead.id,
            name: lead.name,
            success: true,
            place_id: place.id,
            googleName,
          });
        } else {
          results.failed++;
          results.details.push({
            id: lead.id,
            name: lead.name,
            success: false,
            message: "No matching place found or names not similar enough",
            googleName: googleName || null,
          });
        }

        // Add a small delay to avoid hitting rate limits
        await delay(100); // 100ms delay between requests
      } catch (error) {
        console.error(`Error processing lead ${lead.id}:`, error);
        results.failed++;
        results.details.push({
          id: lead.id,
          name: lead.name,
          success: false,
          message: error instanceof Error ? error.message : "Unknown error",
        });
      }
    }

    return NextResponse.json({
      message: `Processed ${results.total} leads, updated ${results.updated}, failed ${results.failed}`,
      ...results,
    });
  } catch (error) {
    console.error("Failed to batch update leads with Google Place IDs:", error);
    return NextResponse.json(
      {
        error: "Failed to batch update leads",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
