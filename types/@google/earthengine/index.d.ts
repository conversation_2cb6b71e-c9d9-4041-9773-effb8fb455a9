/**
 * Type definitions for Google Earth Engine API
 */

declare module "@google/earthengine" {
  namespace ee {
    namespace data {
      function authenticateViaPrivateKey(
        credentials: { client_email: string; private_key: string },
        success: () => void,
        error: (err: Error) => void
      ): void;
    }

    function initialize(
      opt_baseurl: string | null,
      opt_tileurl: string | null,
      success: () => void,
      error: (err: Error) => void
    ): void;

    // Factory functions for Earth Engine objects
    function ImageCollection(id: string): ImageCollection;

    class Filter {
      static date(start: string, end: string): Filter;
    }

    class ImageCollection {
      filter(filter: Filter): ImageCollection;
      first(): Image;
      select(bandName: string): ImageCollection;
      select(bandNames: string[]): ImageCollection;
    }

    class Image {
      sample(options: {
        region: Geometry;
        scale: number;
        geometries?: boolean;
      }): FeatureCollection;
      select(bandName: string): Image;
      select(bandNames: string[]): Image;
      reduceRegions(options: {
        collection: FeatureCollection;
        reducer: any;
        scale: number;
        tileScale?: number;
      }): FeatureCollection;
    }

    class FeatureCollection {
      constructor(features: Feature[] | any[]);
      evaluate(callback: (result: any, error?: any) => void): void;
    }

    class Geometry {
      static Point(coords: [number, number]): Geometry;
    }

    class Feature {
      constructor(geometry: Geometry, properties?: Record<string, any>);
    }

    class Reducer {
      static firstNonNull(): Reducer;
    }
  }

  export = ee;
}
