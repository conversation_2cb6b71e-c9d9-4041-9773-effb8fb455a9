import Link from "next/link";
import { FaMapMarkedAlt, FaR<PERSON>ot, FaChartBar } from "react-icons/fa";

export default function Home() {
  return (
    <div className="h-full w-full bg-gradient-to-b from-white to-sky-50">
      <div className="h-full w-full px-4 py-16 sm:py-24">
        <div className="text-center">
          <h1 className="text-4xl font-extrabold tracking-tight text-gray-900 sm:text-5xl md:text-6xl">
            <span className="block">Bianchi Internal Lead Tool</span>
            <span className="block text-primary">
              Restaurant Lead Management System
            </span>
          </h1>
          <p className="mt-3 mx-auto text-base text-gray-600 sm:text-lg md:mt-5 md:text-xl max-w-3xl">
            Centralized platform for discovering, tracking, and managing restaurant leads 
            across Switzerland. Automate your lead generation process and make data-driven 
            decisions with our integrated tools.
          </p>

          <div className="mt-12 grid grid-cols-1 gap-8 sm:grid-cols-3 mx-auto max-w-7xl">
            <div className="p-6 bg-white rounded-xl shadow-md hover:shadow-lg transition-shadow">
              <div className="text-primary flex justify-center mb-4">
                <FaMapMarkedAlt size={32} />
              </div>
              <h3 className="text-lg font-semibold mb-2">Lead Discovery</h3>
              <p className="text-gray-600">
                Search and filter restaurants by location, automatically identify new openings in your territory
              </p>
            </div>
            <div className="p-6 bg-white rounded-xl shadow-md hover:shadow-lg transition-shadow">
              <div className="text-primary flex justify-center mb-4">
                <FaRobot size={32} />
              </div>
              <h3 className="text-lg font-semibold mb-2">Data Integration</h3>
              <p className="text-gray-600">
                Automatic enrichment with Google Places data and internal systems integration
              </p>
            </div>
            <div className="p-6 bg-white rounded-xl shadow-md hover:shadow-lg transition-shadow">
              <div className="text-primary flex justify-center mb-4">
                <FaChartBar size={32} />
              </div>
              <h3 className="text-lg font-semibold mb-2">Performance Tracking</h3>
              <p className="text-gray-600">
                Monitor lead conversion rates and track sales team performance by region
              </p>
            </div>
          </div>

          <div className="mt-16 grid grid-cols-1 gap-8 sm:grid-cols-2 mx-auto max-w-5xl">
            <div className="p-8 bg-white rounded-xl shadow-md border border-gray-100">
              <h3 className="text-xl font-semibold mb-4">Daily Operations</h3>
              <ul className="text-left space-y-3 text-gray-600">
                <li className="flex items-center gap-2">
                  <span className="text-primary">✓</span> View and manage your assigned territory
                </li>
                <li className="flex items-center gap-2">
                  <span className="text-primary">✓</span> Track lead status updates
                </li>
                <li className="flex items-center gap-2">
                  <span className="text-primary">✓</span> Access customer contact details
                </li>
              </ul>
            </div>
            <div className="p-8 bg-white rounded-xl shadow-md border border-gray-100">
              <h3 className="text-xl font-semibold mb-4">Team Management</h3>
              <ul className="text-left space-y-3 text-gray-600">
                <li className="flex items-center gap-2">
                  <span className="text-primary">✓</span> View team performance metrics
                </li>
                <li className="flex items-center gap-2">
                  <span className="text-primary">✓</span> Monitor regional coverage
                </li>
                <li className="flex items-center gap-2">
                  <span className="text-primary">✓</span> Export data for reporting
                </li>
              </ul>
            </div>
          </div>

          <div className="mt-12 sm:flex sm:justify-center gap-4">
            <Link
              href="/lead-generator"
              className="inline-flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary hover:bg-primary/90 md:py-4 md:text-lg md:px-10 transition-colors"
            >
              Generate New Leads
            </Link>
            <Link
              href="/dashboard"
              className="inline-flex items-center justify-center px-8 py-3 border border-primary text-base font-medium rounded-md text-primary bg-white hover:bg-primary/5 md:py-4 md:text-lg md:px-10 transition-colors mt-3 sm:mt-0"
            >
              View Sales Dashboard
            </Link>
          </div>

          <div className="mt-16 text-center">
            <p className="text-sm text-gray-500">
              Need assistance? Contact <Link href="/contact" className="text-primary hover:underline">IT Support</Link> or view the <Link href="/docs" className="text-primary hover:underline">internal documentation</Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
